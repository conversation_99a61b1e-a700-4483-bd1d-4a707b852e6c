[{"task_id": "chart_1357_point_0", "chart_id": "chart_1357", "chart_title": "Average RO Open Days", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 1, "datasetIndex": 0, "pointIndex": 10, "value": "3.61", "xLabel": "2023-11-01", "screenX": 1714.7514723557692, "screenY": 521.0641552061844, "canvasX": 630.7514723557692, "canvasY": 219.06415520618438, "datasetLabel": "Customer Pay", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Customer Pay", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 631.3926231971154, "y": 155.49720489342553}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 1, "datasetIndex": 0, "pointIndex": 10, "value": "3.61", "xLabel": "2023-11-01", "screenX": 1714.7514723557692, "screenY": 521.0641552061844, "canvasX": 630.7514723557692, "canvasY": 219.06415520618438, "datasetLabel": "Customer Pay", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:07:41.690765", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Customer Pay", "value": "3.61", "html_structure": {"h5_html": "Customer Pay", "h6_html": " 3.61"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Customer Pay", "value": "3.61", "html_structure": {"h5_html": "Customer Pay", "h6_html": " 3.61"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Customer Pay", "value": "3.61", "html_structure": {"h5_html": "Customer Pay", "h6_html": " 3.61"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Customer Pay", "total_items_found": 3}, "error": null}, "timestamp": "2025-09-12T10:07:41.913185", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_1357_point_1", "chart_id": "chart_1357", "chart_title": "Average RO Open Days", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 1, "datasetIndex": 1, "pointIndex": 10, "value": "1.63", "xLabel": "2023-11-01", "screenX": 1714.7514723557692, "screenY": 540.6757039349476, "canvasX": 630.7514723557692, "canvasY": 238.6757039349476, "datasetLabel": "Extended Service", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Extended Service", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 631.3926231971154, "y": 209.97372913998998}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 1, "datasetIndex": 1, "pointIndex": 10, "value": "1.63", "xLabel": "2023-11-01", "screenX": 1714.7514723557692, "screenY": 540.6757039349476, "canvasX": 630.7514723557692, "canvasY": 238.6757039349476, "datasetLabel": "Extended Service", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:08:10.506945", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Extended Service Contract", "value": "1.63", "html_structure": {"h5_html": "Extended Service Contract", "h6_html": " 1.63"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Extended Service Contract", "value": "1.63", "html_structure": {"h5_html": "Extended Service Contract", "h6_html": " 1.63"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Extended Service Contract", "value": "1.63", "html_structure": {"h5_html": "Extended Service Contract", "h6_html": " 1.63"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Extended Service", "total_items_found": 3}, "error": null}, "timestamp": "2025-09-12T10:08:10.770197", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 2, "method": "sequential_processing"}, {"task_id": "chart_1357_point_2", "chart_id": "chart_1357", "chart_title": "Average RO Open Days", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 1, "datasetIndex": 2, "pointIndex": 10, "value": "5.40", "xLabel": "2023-11-01", "screenX": 1714.7514723557692, "screenY": 503.3345227695752, "canvasX": 630.7514723557692, "canvasY": 201.33452276957522, "datasetLabel": "Internal", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Internal", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 630.7514723557692, "y": 199.1059376867612}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 1, "datasetIndex": 2, "pointIndex": 10, "value": "5.40", "xLabel": "2023-11-01", "screenX": 1714.7514723557692, "screenY": 503.3345227695752, "canvasX": 630.7514723557692, "canvasY": 201.33452276957522, "datasetLabel": "Internal", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:08:39.735462", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Internal", "value": "5.40", "html_structure": {"h5_html": "Internal", "h6_html": " 5.40"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Internal", "value": "5.40", "html_structure": {"h5_html": "Internal", "h6_html": " 5.40"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Internal", "value": "5.40", "html_structure": {"h5_html": "Internal", "h6_html": " 5.40"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Internal", "total_items_found": 3}, "error": null}, "timestamp": "2025-09-12T10:08:39.989776", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 3, "method": "sequential_processing"}, {"task_id": "chart_1357_point_3", "chart_id": "chart_1357", "chart_title": "Average RO Open Days", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 1, "datasetIndex": 3, "pointIndex": 10, "value": null, "xLabel": "2023-11-01", "screenX": null, "screenY": null, "canvasX": 630.7514723557692, "canvasY": NaN, "datasetLabel": "Maintenance", "chartType": "bar", "coordinatesValid": false, "targetMonthYear": "2023-11-01"}, "error": "cannot access local variable 'extracted_data' where it is not associated with a value", "timestamp": "2025-09-12T10:09:04.173176", "success": false, "legend_controlled": true, "point_sequence": 4, "method": "sequential_processing"}, {"task_id": "chart_1357_point_4", "chart_id": "chart_1357", "chart_title": "Average RO Open Days", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 1, "datasetIndex": 4, "pointIndex": 10, "value": "6.93", "xLabel": "2023-11-01", "screenX": 1714.7514723557692, "screenY": 488.18014420644, "canvasX": 630.7514723557692, "canvasY": 186.18014420644, "datasetLabel": "Warranty", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Warranty", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 630.7514723557692, "y": 169.0200390687722}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 1, "datasetIndex": 4, "pointIndex": 10, "value": "6.93", "xLabel": "2023-11-01", "screenX": 1714.7514723557692, "screenY": 488.18014420644, "canvasX": 630.7514723557692, "canvasY": 186.18014420644, "datasetLabel": "Warranty", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:09:32.871507", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Warranty", "value": "6.93", "html_structure": {"h5_html": " Warranty", "h6_html": " 6.93"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Warranty", "value": "6.93", "html_structure": {"h5_html": " Warranty", "h6_html": " 6.93"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Warranty", "value": "6.93", "html_structure": {"h5_html": " Warranty", "h6_html": " 6.93"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Warranty", "total_items_found": 3}, "error": null}, "timestamp": "2025-09-12T10:09:33.088479", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 5, "method": "sequential_processing"}, {"task_id": "chart_1357_point_5", "chart_id": "chart_1357", "chart_title": "Average RO Open Days", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 1, "datasetIndex": 5, "pointIndex": 10, "value": null, "xLabel": "2023-11-01", "screenX": null, "screenY": null, "canvasX": 630.7514723557692, "canvasY": NaN, "datasetLabel": "Factory Service Contract", "chartType": "bar", "coordinatesValid": false, "targetMonthYear": "2023-11-01"}, "error": "cannot access local variable 'extracted_data' where it is not associated with a value", "timestamp": "2025-09-12T10:09:52.571340", "success": false, "legend_controlled": true, "point_sequence": 6, "method": "sequential_processing"}, {"task_id": "chart_935_point_0", "chart_id": "chart_935", "chart_title": "Labor Sold Hours Percentage By Pay Type", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 7, "datasetIndex": 0, "pointIndex": 10, "value": "0.57", "xLabel": "2023-11-01", "screenX": 1718.7279597355769, "screenY": 1639.2007964974835, "canvasX": 634.727959735577, "canvasY": 137.2007964974834, "datasetLabel": "Customer Pay", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Customer Pay", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 633.44453125, "y": 154.0036205345731}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 7, "datasetIndex": 0, "pointIndex": 10, "value": "0.57", "xLabel": "2023-11-01", "screenX": 1718.7279597355769, "screenY": 1639.2007964974835, "canvasX": 634.727959735577, "canvasY": 137.2007964974834, "datasetLabel": "Customer Pay", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:10:21.246826", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$83,724.50", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $83,724.50"}}, {"item_index": 1, "title": "Labor Sold Hours - All Categories", "value": "1,121", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,121"}}, {"item_index": 2, "title": "Labor Sold Hours - Customer Pay", "value": "644.2", "html_structure": {"h5_html": "Labor Sold Hours - Customer Pay", "h6_html": " 644.2"}}, {"item_index": 3, "title": "Labor Sold Hours % - Customer Pay", "value": "57%", "html_structure": {"h5_html": "Labor Sold Hours % - Customer Pay", "h6_html": " 57%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$83,724.50", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $83,724.50"}}, {"item_index": 1, "title": "Labor Sold Hours - All Categories", "value": "1,121", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,121"}}, {"item_index": 2, "title": "Labor Sold Hours - Customer Pay", "value": "644.2", "html_structure": {"h5_html": "Labor Sold Hours - Customer Pay", "h6_html": " 644.2"}}, {"item_index": 3, "title": "Labor Sold Hours % - Customer Pay", "value": "57%", "html_structure": {"h5_html": "Labor Sold Hours % - Customer Pay", "h6_html": " 57%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$83,724.50", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $83,724.50"}}, {"item_index": 1, "title": "Labor Sold Hours - All Categories", "value": "1,121", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,121"}}, {"item_index": 2, "title": "Labor Sold Hours - Customer Pay", "value": "644.2", "html_structure": {"h5_html": "Labor Sold Hours - Customer Pay", "h6_html": " 644.2"}}, {"item_index": 3, "title": "Labor Sold Hours % - Customer Pay", "value": "57%", "html_structure": {"h5_html": "Labor Sold Hours % - Customer Pay", "h6_html": " 57%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Customer Pay", "total_items_found": 12}, "error": null}, "timestamp": "2025-09-12T10:10:24.730993", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_935_point_1", "chart_id": "chart_935", "chart_title": "Labor Sold Hours Percentage By Pay Type", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 7, "datasetIndex": 1, "pointIndex": 10, "value": "0.05", "xLabel": "2023-11-01", "screenX": 1718.7279597355769, "screenY": 1746.5030412255649, "canvasX": 634.727959735577, "canvasY": 244.50304122556494, "datasetLabel": "Extended Service", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Extended Service", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 632.161102764423, "y": 177.43913827051398}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 7, "datasetIndex": 1, "pointIndex": 10, "value": "0.05", "xLabel": "2023-11-01", "screenX": 1718.7279597355769, "screenY": 1746.5030412255649, "canvasX": 634.727959735577, "canvasY": 244.50304122556494, "datasetLabel": "Extended Service", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:10:53.350841", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sold Hours - All Categories", "value": "1,121", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,121"}}, {"item_index": 1, "title": "Labor Sold Hours - Extended Service", "value": "56.3", "html_structure": {"h5_html": "Labor Sold Hours - Extended Service", "h6_html": " 56.3"}}, {"item_index": 2, "title": "Labor Sold Hours % - Extended Service", "value": "5%", "html_structure": {"h5_html": "Labor Sold Hours % - Extended Service", "h6_html": " 5%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sold Hours - All Categories", "value": "1,121", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,121"}}, {"item_index": 1, "title": "Labor Sold Hours - Extended Service", "value": "56.3", "html_structure": {"h5_html": "Labor Sold Hours - Extended Service", "h6_html": " 56.3"}}, {"item_index": 2, "title": "Labor Sold Hours % - Extended Service", "value": "5%", "html_structure": {"h5_html": "Labor Sold Hours % - Extended Service", "h6_html": " 5%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sold Hours - All Categories", "value": "1,121", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,121"}}, {"item_index": 1, "title": "Labor Sold Hours - Extended Service", "value": "56.3", "html_structure": {"h5_html": "Labor Sold Hours - Extended Service", "h6_html": " 56.3"}}, {"item_index": 2, "title": "Labor Sold Hours % - Extended Service", "value": "5%", "html_structure": {"h5_html": "Labor Sold Hours % - Extended Service", "h6_html": " 5%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Extended Service", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-12T10:10:56.181146", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 2, "method": "sequential_processing"}, {"task_id": "chart_935_point_2", "chart_id": "chart_935", "chart_title": "Labor Sold Hours Percentage By Pay Type", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 7, "datasetIndex": 2, "pointIndex": 10, "value": "0.18", "xLabel": "2023-11-01", "screenX": 1718.7279597355769, "screenY": 1719.6774800435446, "canvasX": 634.727959735577, "canvasY": 217.67748004354456, "datasetLabel": "Internal", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Internal", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 633.44453125, "y": 143.3913106164112}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 7, "datasetIndex": 2, "pointIndex": 10, "value": "0.18", "xLabel": "2023-11-01", "screenX": 1718.7279597355769, "screenY": 1719.6774800435446, "canvasX": 634.727959735577, "canvasY": 217.67748004354456, "datasetLabel": "Internal", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:11:24.591481", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sold Hours - All Categories", "value": "1,121", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,121"}}, {"item_index": 1, "title": "Labor Sold Hours - Internal", "value": "204.40", "html_structure": {"h5_html": "Labor Sold Hours - Internal", "h6_html": " 204.40"}}, {"item_index": 2, "title": "Labor Sold Hours % - Internal", "value": "18%", "html_structure": {"h5_html": "Labor Sold Hours % - Internal", "h6_html": " 18%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sold Hours - All Categories", "value": "1,121", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,121"}}, {"item_index": 1, "title": "Labor Sold Hours - Internal", "value": "204.40", "html_structure": {"h5_html": "Labor Sold Hours - Internal", "h6_html": " 204.40"}}, {"item_index": 2, "title": "Labor Sold Hours % - Internal", "value": "18%", "html_structure": {"h5_html": "Labor Sold Hours % - Internal", "h6_html": " 18%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sold Hours - All Categories", "value": "1,121", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,121"}}, {"item_index": 1, "title": "Labor Sold Hours - Internal", "value": "204.40", "html_structure": {"h5_html": "Labor Sold Hours - Internal", "h6_html": " 204.40"}}, {"item_index": 2, "title": "Labor Sold Hours % - Internal", "value": "18%", "html_structure": {"h5_html": "Labor Sold Hours % - Internal", "h6_html": " 18%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Internal", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-12T10:11:27.783515", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 3, "method": "sequential_processing"}, {"task_id": "chart_935_point_3", "chart_id": "chart_935", "chart_title": "Labor Sold Hours Percentage By Pay Type", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 7, "datasetIndex": 3, "pointIndex": 10, "value": null, "xLabel": "2023-11-01", "screenX": null, "screenY": null, "canvasX": 634.727959735577, "canvasY": NaN, "datasetLabel": "Maintenance Plan", "chartType": "bar", "coordinatesValid": false, "targetMonthYear": "2023-11-01"}, "error": "cannot access local variable 'extracted_data' where it is not associated with a value", "timestamp": "2025-09-12T10:11:47.286663", "success": false, "legend_controlled": true, "point_sequence": 4, "method": "sequential_processing"}, {"task_id": "chart_935_point_4", "chart_id": "chart_935", "chart_title": "Labor Sold Hours Percentage By Pay Type", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 7, "datasetIndex": 4, "pointIndex": 10, "value": "0.19", "xLabel": "2023-11-01", "screenX": 1718.7279597355769, "screenY": 1717.6139753372354, "canvasX": 634.727959735577, "canvasY": 215.6139753372353, "datasetLabel": "Warranty", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Warranty", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 633.44453125, "y": 176.40738591735933}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 7, "datasetIndex": 4, "pointIndex": 10, "value": "0.19", "xLabel": "2023-11-01", "screenX": 1718.7279597355769, "screenY": 1717.6139753372354, "canvasX": 634.727959735577, "canvasY": 215.6139753372353, "datasetLabel": "Warranty", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:12:16.203657", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sold Hours - All Categories", "value": "1,121", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,121"}}, {"item_index": 1, "title": "Labor Sold Hours - Warranty", "value": "216.1", "html_structure": {"h5_html": "Labor Sold Hours - Warranty", "h6_html": " 216.1"}}, {"item_index": 2, "title": "Labor Sold Hours % - Warranty", "value": "19%", "html_structure": {"h5_html": "Labor Sold Hours % - Warranty", "h6_html": " 19%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sold Hours - All Categories", "value": "1,121", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,121"}}, {"item_index": 1, "title": "Labor Sold Hours - Warranty", "value": "216.1", "html_structure": {"h5_html": "Labor Sold Hours - Warranty", "h6_html": " 216.1"}}, {"item_index": 2, "title": "Labor Sold Hours % - Warranty", "value": "19%", "html_structure": {"h5_html": "Labor Sold Hours % - Warranty", "h6_html": " 19%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sold Hours - All Categories", "value": "1,121", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,121"}}, {"item_index": 1, "title": "Labor Sold Hours - Warranty", "value": "216.1", "html_structure": {"h5_html": "Labor Sold Hours - Warranty", "h6_html": " 216.1"}}, {"item_index": 2, "title": "Labor Sold Hours % - Warranty", "value": "19%", "html_structure": {"h5_html": "Labor Sold Hours % - Warranty", "h6_html": " 19%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Warranty", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-12T10:12:19.094310", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 5, "method": "sequential_processing"}, {"task_id": "chart_935_point_5", "chart_id": "chart_935", "chart_title": "Labor Sold Hours Percentage By Pay Type", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 7, "datasetIndex": 5, "pointIndex": 10, "value": null, "xLabel": "2023-11-01", "screenX": null, "screenY": null, "canvasX": 634.727959735577, "canvasY": NaN, "datasetLabel": "Factory Service Contract", "chartType": "bar", "coordinatesValid": false, "targetMonthYear": "2023-11-01"}, "error": "cannot access local variable 'extracted_data' where it is not associated with a value", "timestamp": "2025-09-12T10:12:38.658684", "success": false, "legend_controlled": true, "point_sequence": 6, "method": "sequential_processing"}, {"task_id": "chart_948_point_0", "chart_id": "chart_948", "chart_title": "CP 1-Line-RO Count", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 0, "datasetIndex": 0, "pointIndex": 10, "value": "188", "xLabel": "2023-11-01", "screenX": 907.0349008413461, "screenY": 463.7152324084374, "canvasX": 632.0349008413461, "canvasY": 161.7152324084374, "datasetLabel": "Mileage Under 60k", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Mileage Under 60k", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 632.0349008413461, "y": 138.43889932126896}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 0, "datasetIndex": 0, "pointIndex": 10, "value": "188", "xLabel": "2023-11-01", "screenX": 907.0349008413461, "screenY": 463.7152324084374, "canvasX": 632.0349008413461, "canvasY": 161.7152324084374, "datasetLabel": "Mileage Under 60k", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:13:04.651149", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Mileage Under 60K", "value": "188", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 188"}}, {"item_index": 1, "title": "Labor Sale", "value": "$11,625.95", "html_structure": {"h5_html": "Labor Sale", "h6_html": " $11,625.95"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "99.70", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 99.70"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Mileage Under 60K", "value": "188", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 188"}}, {"item_index": 1, "title": "Labor Sale", "value": "$11,625.95", "html_structure": {"h5_html": "Labor Sale", "h6_html": " $11,625.95"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "99.70", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 99.70"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Mileage Under 60K", "value": "188", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 188"}}, {"item_index": 1, "title": "Labor Sale", "value": "$11,625.95", "html_structure": {"h5_html": "Labor Sale", "h6_html": " $11,625.95"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "99.70", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 99.70"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Mileage Under 60k", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-12T10:13:05.073514", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_948_point_1", "chart_id": "chart_948", "chart_title": "CP 1-Line-RO Count", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 0, "datasetIndex": 1, "pointIndex": 10, "value": "215", "xLabel": "2023-11-01", "screenX": 907.0349008413461, "screenY": 450.3437219115534, "canvasX": 632.0349008413461, "canvasY": 148.34372191155342, "datasetLabel": "Mileage Over 60k", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Mileage Over 60k", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 632.0349008413461, "y": 166.08986238581303}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 0, "datasetIndex": 1, "pointIndex": 10, "value": "215", "xLabel": "2023-11-01", "screenX": 907.0349008413461, "screenY": 450.3437219115534, "canvasX": 632.0349008413461, "canvasY": 148.34372191155342, "datasetLabel": "Mileage Over 60k", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:13:33.391497", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Mileage Over 60K", "value": "215", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 215"}}, {"item_index": 1, "title": "Labor Sale", "value": "$18,512.61", "html_structure": {"h5_html": "Labor Sale", "h6_html": " $18,512.61"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "135.60", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 135.60"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Mileage Over 60K", "value": "215", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 215"}}, {"item_index": 1, "title": "Labor Sale", "value": "$18,512.61", "html_structure": {"h5_html": "Labor Sale", "h6_html": " $18,512.61"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "135.60", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 135.60"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Mileage Over 60K", "value": "215", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 215"}}, {"item_index": 1, "title": "Labor Sale", "value": "$18,512.61", "html_structure": {"h5_html": "Labor Sale", "h6_html": " $18,512.61"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "135.60", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 135.60"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Mileage Over 60k", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-12T10:13:33.759129", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 2, "method": "sequential_processing"}, {"task_id": "chart_948_point_2", "chart_id": "chart_948", "chart_title": "CP 1-Line-RO Count", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 0, "datasetIndex": 2, "pointIndex": 10, "value": "403", "xLabel": "2023-11-01", "screenX": 907.0349008413461, "screenY": 357.23838956287955, "canvasX": 632.0349008413461, "canvasY": 55.238389562879576, "datasetLabel": "Total Shop", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Total Shop", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 632.0349008413461, "y": 155.0294771599954}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 0, "datasetIndex": 2, "pointIndex": 10, "value": "403", "xLabel": "2023-11-01", "screenX": 907.0349008413461, "screenY": 357.23838956287955, "canvasX": 632.0349008413461, "canvasY": 55.238389562879576, "datasetLabel": "Total Shop", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:14:02.363491", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Total Shop", "value": "403", "html_structure": {"h5_html": "Total Shop", "h6_html": " 403"}}, {"item_index": 1, "title": "Labor Sale", "value": "$30,138.56", "html_structure": {"h5_html": "Labor Sale", "h6_html": " $30,138.56"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "235.30", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 235.30"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Total Shop", "value": "403", "html_structure": {"h5_html": "Total Shop", "h6_html": " 403"}}, {"item_index": 1, "title": "Labor Sale", "value": "$30,138.56", "html_structure": {"h5_html": "Labor Sale", "h6_html": " $30,138.56"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "235.30", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 235.30"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Total Shop", "value": "403", "html_structure": {"h5_html": "Total Shop", "h6_html": " 403"}}, {"item_index": 1, "title": "Labor Sale", "value": "$30,138.56", "html_structure": {"h5_html": "Labor Sale", "h6_html": " $30,138.56"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "235.30", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 235.30"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Total Shop", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-12T10:14:02.755714", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 3, "method": "sequential_processing"}, {"task_id": "chart_923_point_0", "chart_id": "chart_923", "chart_title": "CP 1-Line-RO Count Percentage", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 2, "datasetIndex": 0, "pointIndex": 10, "value": "70.15", "xLabel": "2023-11-01", "screenX": 908.44453125, "screenY": 739.6882820357193, "canvasX": 633.44453125, "canvasY": 37.688282035719304, "datasetLabel": "Mileage Under 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Mileage Under 60K", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 633.44453125, "y": 146.25442339641526}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 2, "datasetIndex": 0, "pointIndex": 10, "value": "70.15", "xLabel": "2023-11-01", "screenX": 908.44453125, "screenY": 739.6882820357193, "canvasX": 633.44453125, "canvasY": 37.688282035719304, "datasetLabel": "Mileage Under 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:14:29.011460", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Total RO Count", "value": "188", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 188"}}, {"item_index": 1, "title": "Mileage Under 60K", "value": "71.48%", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 71.48%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Total RO Count", "value": "188", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 188"}}, {"item_index": 1, "title": "Mileage Under 60K", "value": "71.48%", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 71.48%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Total RO Count", "value": "188", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 188"}}, {"item_index": 1, "title": "Mileage Under 60K", "value": "71.48%", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 71.48%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Mileage Under 60K", "total_items_found": 6}, "error": null}, "timestamp": "2025-09-12T10:14:29.336207", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_923_point_1", "chart_id": "chart_923", "chart_title": "CP 1-Line-RO Count Percentage", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 2, "datasetIndex": 1, "pointIndex": 10, "value": "67.40", "xLabel": "2023-11-01", "screenX": 908.44453125, "screenY": 748.200238949245, "canvasX": 633.44453125, "canvasY": 46.20023894924502, "datasetLabel": "Mileage Over 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Mileage Over 60K", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 633.44453125, "y": 150.51040185317814}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 2, "datasetIndex": 1, "pointIndex": 10, "value": "67.40", "xLabel": "2023-11-01", "screenX": 908.44453125, "screenY": 748.200238949245, "canvasX": 633.44453125, "canvasY": 46.20023894924502, "datasetLabel": "Mileage Over 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:14:57.966854", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Total RO Count", "value": "0", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 0"}}, {"item_index": 1, "title": "Mileage Over 60K", "value": "69.35%", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 69.35%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Total RO Count", "value": "0", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 0"}}, {"item_index": 1, "title": "Mileage Over 60K", "value": "69.35%", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 69.35%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Total RO Count", "value": "0", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 0"}}, {"item_index": 1, "title": "Mileage Over 60K", "value": "69.35%", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 69.35%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Mileage Over 60K", "total_items_found": 6}, "error": null}, "timestamp": "2025-09-12T10:14:58.294517", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 2, "method": "sequential_processing"}, {"task_id": "chart_923_point_2", "chart_id": "chart_923", "chart_title": "CP 1-Line-RO Count Percentage", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 2, "datasetIndex": 2, "pointIndex": 10, "value": "68.65", "xLabel": "2023-11-01", "screenX": 908.44453125, "screenY": 744.3311676249151, "canvasX": 633.44453125, "canvasY": 42.33116762491515, "datasetLabel": "Total Shop", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Total Shop", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 633.44453125, "y": 148.5758661910132}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 2, "datasetIndex": 2, "pointIndex": 10, "value": "68.65", "xLabel": "2023-11-01", "screenX": 908.44453125, "screenY": 744.3311676249151, "canvasX": 633.44453125, "canvasY": 42.33116762491515, "datasetLabel": "Total Shop", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:15:26.991333", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Total RO Count", "value": "188", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 188"}}, {"item_index": 1, "title": "Total Shop", "value": "70.33%", "html_structure": {"h5_html": "Total Shop", "h6_html": " 70.33%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Total RO Count", "value": "188", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 188"}}, {"item_index": 1, "title": "Total Shop", "value": "70.33%", "html_structure": {"h5_html": "Total Shop", "h6_html": " 70.33%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Total RO Count", "value": "188", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 188"}}, {"item_index": 1, "title": "Total Shop", "value": "70.33%", "html_structure": {"h5_html": "Total Shop", "h6_html": " 70.33%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Total Shop", "total_items_found": 6}, "error": null}, "timestamp": "2025-09-12T10:15:27.302397", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 3, "method": "sequential_processing"}, {"task_id": "chart_1354_point_0", "chart_id": "chart_1354", "chart_title": "Multi-Line-RO Count", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 3, "datasetIndex": 0, "pointIndex": 10, "value": "80", "xLabel": "2023-11-01", "screenX": 1716.0349008413461, "screenY": 877.5819840348356, "canvasX": 632.0349008413461, "canvasY": 175.5819840348356, "datasetLabel": "Mileage Under 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Mileage Under 60K", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 632.0349008413461, "y": 155.77233885426674}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1354", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 3, "datasetIndex": 0, "pointIndex": 10, "value": "80", "xLabel": "2023-11-01", "screenX": 1716.0349008413461, "screenY": 877.5819840348356, "canvasX": 632.0349008413461, "canvasY": 175.5819840348356, "datasetLabel": "Mileage Under 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:15:53.083546", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1354", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Mileage Under 60K", "value": "80", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 80"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Mileage Under 60K", "value": "80", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 80"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Mileage Under 60K", "value": "80", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 80"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Mileage Under 60K", "total_items_found": 3}, "error": null}, "timestamp": "2025-09-12T10:15:53.336963", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1354", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_1354_point_1", "chart_id": "chart_1354", "chart_title": "Multi-Line-RO Count", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 3, "datasetIndex": 1, "pointIndex": 10, "value": "104", "xLabel": "2023-11-01", "screenX": 1716.0349008413461, "screenY": 853.810409818153, "canvasX": 632.0349008413461, "canvasY": 151.81040981815298, "datasetLabel": "Mileage Over 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Mileage Over 60K", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 632.0349008413461, "y": 168.97876897464602}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1354", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 3, "datasetIndex": 1, "pointIndex": 10, "value": "104", "xLabel": "2023-11-01", "screenX": 1716.0349008413461, "screenY": 853.810409818153, "canvasX": 632.0349008413461, "canvasY": 151.81040981815298, "datasetLabel": "Mileage Over 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:16:21.886629", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1354", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Mileage Over 60K", "value": "104", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 104"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Mileage Over 60K", "value": "104", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 104"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Mileage Over 60K", "value": "104", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 104"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Mileage Over 60K", "total_items_found": 3}, "error": null}, "timestamp": "2025-09-12T10:16:22.076615", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1354", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 2, "method": "sequential_processing"}, {"task_id": "chart_1354_point_2", "chart_id": "chart_1354", "chart_title": "Multi-Line-RO Count", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 3, "datasetIndex": 2, "pointIndex": 10, "value": "184", "xLabel": "2023-11-01", "screenX": 1716.0349008413461, "screenY": 774.5718290958773, "canvasX": 632.0349008413461, "canvasY": 72.57182909587738, "datasetLabel": "Total Shop", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Total Shop", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 632.0349008413461, "y": 163.6961969264943}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1354", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 3, "datasetIndex": 2, "pointIndex": 10, "value": "184", "xLabel": "2023-11-01", "screenX": 1716.0349008413461, "screenY": 774.5718290958773, "canvasX": 632.0349008413461, "canvasY": 72.57182909587738, "datasetLabel": "Total Shop", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:16:50.687601", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1354", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Total Shop", "value": "184", "html_structure": {"h5_html": "Total Shop", "h6_html": " 184"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Total Shop", "value": "184", "html_structure": {"h5_html": "Total Shop", "h6_html": " 184"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Total Shop", "value": "184", "html_structure": {"h5_html": "Total Shop", "h6_html": " 184"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Total Shop", "total_items_found": 3}, "error": null}, "timestamp": "2025-09-12T10:16:50.909110", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1354", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 3, "method": "sequential_processing"}, {"task_id": "chart_1355_point_0", "chart_id": "chart_1355", "chart_title": "Multi-Line-RO Count Percentage", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 4, "datasetIndex": 0, "pointIndex": 10, "value": "29.85", "xLabel": "2023-11-01", "screenX": 908.44453125, "screenY": 1172.033718307117, "canvasX": 633.44453125, "canvasY": 70.03371830711696, "datasetLabel": "Mileage Under 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Mileage Under 60K", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 633.44453125, "y": 162.4271415321141}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 4, "datasetIndex": 0, "pointIndex": 10, "value": "29.85", "xLabel": "2023-11-01", "screenX": 908.44453125, "screenY": 1172.033718307117, "canvasX": 633.44453125, "canvasY": 70.03371830711696, "datasetLabel": "Mileage Under 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:17:17.683528", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Total RO Count", "value": "80", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 80"}}, {"item_index": 1, "title": "Mileage Under 60K", "value": "30.42%", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 30.42%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Total RO Count", "value": "80", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 80"}}, {"item_index": 1, "title": "Mileage Under 60K", "value": "30.42%", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 30.42%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Total RO Count", "value": "80", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 80"}}, {"item_index": 1, "title": "Mileage Under 60K", "value": "30.42%", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 30.42%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Mileage Under 60K", "total_items_found": 6}, "error": null}, "timestamp": "2025-09-12T10:17:18.033255", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_1355_point_1", "chart_id": "chart_1355", "chart_title": "Multi-Line-RO Count Percentage", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 4, "datasetIndex": 1, "pointIndex": 10, "value": "32.60", "xLabel": "2023-11-01", "screenX": 908.44453125, "screenY": 1155.0098044800657, "canvasX": 633.44453125, "canvasY": 53.00980448006557, "datasetLabel": "Mileage Over 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Mileage Over 60K", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 633.44453125, "y": 153.9151846185884}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 4, "datasetIndex": 1, "pointIndex": 10, "value": "32.60", "xLabel": "2023-11-01", "screenX": 908.44453125, "screenY": 1155.0098044800657, "canvasX": 633.44453125, "canvasY": 53.00980448006557, "datasetLabel": "Mileage Over 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:17:46.428376", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Total RO Count", "value": "104", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 104"}}, {"item_index": 1, "title": "Mileage Over 60K", "value": "33.55%", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 33.55%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Total RO Count", "value": "104", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 104"}}, {"item_index": 1, "title": "Mileage Over 60K", "value": "33.55%", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 33.55%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Total RO Count", "value": "104", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 104"}}, {"item_index": 1, "title": "Mileage Over 60K", "value": "33.55%", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 33.55%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Mileage Over 60K", "total_items_found": 6}, "error": null}, "timestamp": "2025-09-12T10:17:46.721772", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 2, "method": "sequential_processing"}, {"task_id": "chart_1355_point_2", "chart_id": "chart_1355", "chart_title": "Multi-Line-RO Count Percentage", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 4, "datasetIndex": 2, "pointIndex": 10, "value": "31.35", "xLabel": "2023-11-01", "screenX": 908.44453125, "screenY": 1162.7479471287254, "canvasX": 633.44453125, "canvasY": 60.7479471287253, "datasetLabel": "Total Shop", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Total Shop", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 633.44453125, "y": 157.78425594291826}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 4, "datasetIndex": 2, "pointIndex": 10, "value": "31.35", "xLabel": "2023-11-01", "screenX": 908.44453125, "screenY": 1162.7479471287254, "canvasX": 633.44453125, "canvasY": 60.7479471287253, "datasetLabel": "Total Shop", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:18:15.319638", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Total RO Count", "value": "184", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 184"}}, {"item_index": 1, "title": "Total Shop", "value": "18.15%", "html_structure": {"h5_html": "Total Shop", "h6_html": " 18.15%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Total RO Count", "value": "184", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 184"}}, {"item_index": 1, "title": "Total Shop", "value": "18.15%", "html_structure": {"h5_html": "Total Shop", "h6_html": " 18.15%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Total RO Count", "value": "184", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 184"}}, {"item_index": 1, "title": "Total Shop", "value": "18.15%", "html_structure": {"h5_html": "Total Shop", "h6_html": " 18.15%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Total Shop", "total_items_found": 6}, "error": null}, "timestamp": "2025-09-12T10:18:15.603155", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 3, "method": "sequential_processing"}, {"task_id": "chart_936_point_0", "chart_id": "chart_936", "chart_title": "CP Parts to Labor Ratio By Category", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 8, "datasetIndex": 0, "pointIndex": 10, "value": "3.37", "xLabel": "2023-11-01", "screenX": 906.3926231971154, "screenY": 1948.200238949245, "canvasX": 631.3926231971154, "canvasY": 46.20023894924502, "datasetLabel": "Competitive", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Competitive", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 631.3926231971154, "y": 150.51040185317814}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 8, "datasetIndex": 0, "pointIndex": 10, "value": "3.37", "xLabel": "2023-11-01", "screenX": 906.3926231971154, "screenY": 1948.200238949245, "canvasX": 631.3926231971154, "canvasY": 46.20023894924502, "datasetLabel": "Competitive", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:18:41.234925", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sale - Competitive", "value": "$8,392.91", "html_structure": {"h5_html": "Labor Sale - Competitive", "h6_html": " $8,392.91"}}, {"item_index": 1, "title": "Parts Sale - Competitive", "value": "$28,246.23", "html_structure": {"h5_html": "Parts Sale - Competitive", "h6_html": " $28,246.23"}}, {"item_index": 2, "title": "Parts To Labor Ratio - Competitive", "value": "$3.37", "html_structure": {"h5_html": "Parts To Labor Ratio - Competitive", "h6_html": " $3.37"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sale - Competitive", "value": "$8,392.91", "html_structure": {"h5_html": "Labor Sale - Competitive", "h6_html": " $8,392.91"}}, {"item_index": 1, "title": "Parts Sale - Competitive", "value": "$28,246.23", "html_structure": {"h5_html": "Parts Sale - Competitive", "h6_html": " $28,246.23"}}, {"item_index": 2, "title": "Parts To Labor Ratio - Competitive", "value": "$3.37", "html_structure": {"h5_html": "Parts To Labor Ratio - Competitive", "h6_html": " $3.37"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sale - Competitive", "value": "$8,392.91", "html_structure": {"h5_html": "Labor Sale - Competitive", "h6_html": " $8,392.91"}}, {"item_index": 1, "title": "Parts Sale - Competitive", "value": "$28,246.23", "html_structure": {"h5_html": "Parts Sale - Competitive", "h6_html": " $28,246.23"}}, {"item_index": 2, "title": "Parts To Labor Ratio - Competitive", "value": "$3.37", "html_structure": {"h5_html": "Parts To Labor Ratio - Competitive", "h6_html": " $3.37"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Competitive", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-12T10:18:41.618120", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_936_point_1", "chart_id": "chart_936", "chart_title": "CP Parts to Labor Ratio By Category", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 8, "datasetIndex": 1, "pointIndex": 10, "value": "1.72", "xLabel": "2023-11-01", "screenX": 906.3926231971154, "screenY": 2050.3437219115535, "canvasX": 631.3926231971154, "canvasY": 148.34372191155342, "datasetLabel": "Maintenance", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Maintenance", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 631.3926231971154, "y": 183.83600286007268}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 8, "datasetIndex": 1, "pointIndex": 10, "value": "1.72", "xLabel": "2023-11-01", "screenX": 906.3926231971154, "screenY": 2050.3437219115535, "canvasX": 631.3926231971154, "canvasY": 148.34372191155342, "datasetLabel": "Maintenance", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:19:10.155005", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sale - Maintenance", "value": "$33,313.1", "html_structure": {"h5_html": "Labor Sale - Maintenance", "h6_html": " $33,313.1"}}, {"item_index": 1, "title": "Parts Sale - Maintenance", "value": "$57,448.85", "html_structure": {"h5_html": "Parts Sale - Maintenance", "h6_html": " $57,448.85"}}, {"item_index": 2, "title": "Parts To Labor Ratio - Maintenance", "value": "$1.72", "html_structure": {"h5_html": "Parts To Labor Ratio - Maintenance", "h6_html": " $1.72"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sale - Maintenance", "value": "$33,313.1", "html_structure": {"h5_html": "Labor Sale - Maintenance", "h6_html": " $33,313.1"}}, {"item_index": 1, "title": "Parts Sale - Maintenance", "value": "$57,448.85", "html_structure": {"h5_html": "Parts Sale - Maintenance", "h6_html": " $57,448.85"}}, {"item_index": 2, "title": "Parts To Labor Ratio - Maintenance", "value": "$1.72", "html_structure": {"h5_html": "Parts To Labor Ratio - Maintenance", "h6_html": " $1.72"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sale - Maintenance", "value": "$33,313.1", "html_structure": {"h5_html": "Labor Sale - Maintenance", "h6_html": " $33,313.1"}}, {"item_index": 1, "title": "Parts Sale - Maintenance", "value": "$57,448.85", "html_structure": {"h5_html": "Parts Sale - Maintenance", "h6_html": " $57,448.85"}}, {"item_index": 2, "title": "Parts To Labor Ratio - Maintenance", "value": "$1.72", "html_structure": {"h5_html": "Parts To Labor Ratio - Maintenance", "h6_html": " $1.72"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Maintenance", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-12T10:19:10.609834", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 2, "method": "sequential_processing"}, {"task_id": "chart_936_point_2", "chart_id": "chart_936", "chart_title": "CP Parts to Labor Ratio By Category", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 8, "datasetIndex": 2, "pointIndex": 10, "value": "0.95", "xLabel": "2023-11-01", "screenX": 906.3926231971154, "screenY": 2098.0106806272975, "canvasX": 631.3926231971154, "canvasY": 196.01068062729732, "datasetLabel": "Repair", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Repair", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 633.3172025240385, "y": 176.40738591735936}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 8, "datasetIndex": 2, "pointIndex": 10, "value": "0.95", "xLabel": "2023-11-01", "screenX": 906.3926231971154, "screenY": 2098.0106806272975, "canvasX": 631.3926231971154, "canvasY": 196.01068062729732, "datasetLabel": "Repair", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:19:39.151629", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sale - Repair", "value": "$42,018.49", "html_structure": {"h5_html": "Labor Sale - Repair", "h6_html": " $42,018.49"}}, {"item_index": 1, "title": "Parts Sale - Repair", "value": "$39,936.01", "html_structure": {"h5_html": "Parts Sale - Repair", "h6_html": " $39,936.01"}}, {"item_index": 2, "title": "Parts To Labor Ratio - Repair", "value": "$0.95", "html_structure": {"h5_html": "Parts To Labor Ratio - Repair", "h6_html": " $0.95"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sale - Repair", "value": "$42,018.49", "html_structure": {"h5_html": "Labor Sale - Repair", "h6_html": " $42,018.49"}}, {"item_index": 1, "title": "Parts Sale - Repair", "value": "$39,936.01", "html_structure": {"h5_html": "Parts Sale - Repair", "h6_html": " $39,936.01"}}, {"item_index": 2, "title": "Parts To Labor Ratio - Repair", "value": "$0.95", "html_structure": {"h5_html": "Parts To Labor Ratio - Repair", "h6_html": " $0.95"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sale - Repair", "value": "$42,018.49", "html_structure": {"h5_html": "Labor Sale - Repair", "h6_html": " $42,018.49"}}, {"item_index": 1, "title": "Parts Sale - Repair", "value": "$39,936.01", "html_structure": {"h5_html": "Parts Sale - Repair", "h6_html": " $39,936.01"}}, {"item_index": 2, "title": "Parts To Labor Ratio - Repair", "value": "$0.95", "html_structure": {"h5_html": "Parts To Labor Ratio - Repair", "h6_html": " $0.95"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Repair", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-12T10:19:39.541761", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 3, "method": "sequential_processing"}, {"task_id": "chart_1239_point_0", "chart_id": "chart_1239", "chart_title": "Revenue - Shop Supplies", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 9, "datasetIndex": 0, "pointIndex": 10, "value": null, "xLabel": "2023-11-01", "screenX": null, "screenY": null, "canvasX": 633.3172025240385, "canvasY": NaN, "datasetLabel": "Customer Pay", "chartType": "bar", "coordinatesValid": false, "targetMonthYear": "2023-11-01"}, "error": "cannot access local variable 'extracted_data' where it is not associated with a value", "timestamp": "2025-09-12T10:19:56.513283", "success": false, "legend_controlled": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_1239_point_1", "chart_id": "chart_1239", "chart_title": "Revenue - Shop Supplies", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 9, "datasetIndex": 1, "pointIndex": 10, "value": null, "xLabel": "2023-11-01", "screenX": null, "screenY": null, "canvasX": 633.3172025240385, "canvasY": NaN, "datasetLabel": "Warranty", "chartType": "bar", "coordinatesValid": false, "targetMonthYear": "2023-11-01"}, "error": "cannot access local variable 'extracted_data' where it is not associated with a value", "timestamp": "2025-09-12T10:20:15.695743", "success": false, "legend_controlled": true, "point_sequence": 2, "method": "sequential_processing"}, {"task_id": "chart_1239_point_2", "chart_id": "chart_1239", "chart_title": "Revenue - Shop Supplies", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 9, "datasetIndex": 2, "pointIndex": 10, "value": null, "xLabel": "2023-11-01", "screenX": null, "screenY": null, "canvasX": 633.3172025240385, "canvasY": NaN, "datasetLabel": "Internal", "chartType": "bar", "coordinatesValid": false, "targetMonthYear": "2023-11-01"}, "error": "cannot access local variable 'extracted_data' where it is not associated with a value", "timestamp": "2025-09-12T10:20:35.259061", "success": false, "legend_controlled": true, "point_sequence": 3, "method": "sequential_processing"}, {"task_id": "chart_938_point_0", "chart_id": "chart_938", "chart_title": "CP Return Rate", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 5, "datasetIndex": 0, "pointIndex": 10, "value": "8.73", "xLabel": "2023-11", "screenX": 1714.7514723557692, "screenY": 1221.7125941115123, "canvasX": 630.7514723557692, "canvasY": 119.71259411151242, "datasetLabel": "12 Months Return Rate", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "12 Months Return Rate", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 630.7514723557692, "y": 164.74858432671203}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 5, "datasetIndex": 0, "pointIndex": 10, "value": "8.73", "xLabel": "2023-11", "screenX": 1714.7514723557692, "screenY": 1221.7125941115123, "canvasX": 630.7514723557692, "canvasY": 119.71259411151242, "datasetLabel": "12 Months Return Rate", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:21:11.418876", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": false, "error": null, "attempt": 3, "chart_id": "", "dataset_label": "12 Months Return Rate", "total_items_found": 0}, "error": "Data extraction failed after 3 attempts"}, "timestamp": "2025-09-12T10:21:11.531463", "success": false, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": false, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_938_point_1", "chart_id": "chart_938", "chart_title": "CP Return Rate", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 5, "datasetIndex": 1, "pointIndex": 10, "value": "10.52", "xLabel": "2023-11", "screenX": 1714.7514723557692, "screenY": 1194.0100434293106, "canvasX": 630.7514723557692, "canvasY": 92.01004342931061, "datasetLabel": "6 Months Return Rate", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "6 Months Return Rate", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 630.7514723557692, "y": 173.41530409321092}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 5, "datasetIndex": 1, "pointIndex": 10, "value": "10.52", "xLabel": "2023-11", "screenX": 1714.7514723557692, "screenY": 1194.0100434293106, "canvasX": 630.7514723557692, "canvasY": 92.01004342931061, "datasetLabel": "6 Months Return Rate", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:21:50.339625", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": false, "error": null, "attempt": 3, "chart_id": "", "dataset_label": "6 Months Return Rate", "total_items_found": 0}, "error": "Data extraction failed after 3 attempts"}, "timestamp": "2025-09-12T10:21:50.422526", "success": false, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": false, "point_sequence": 2, "method": "sequential_processing"}, {"task_id": "chart_930_point_0", "chart_id": "chart_930", "chart_title": "CP Parts to Labor Ratio", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 6, "datasetIndex": 0, "pointIndex": 10, "value": "1.50", "xLabel": "2023-11-01", "screenX": 908.3172025240385, "screenY": 1550.4700941261851, "canvasX": 633.3172025240385, "canvasY": 48.47009412618523, "datasetLabel": "Parts to Labor Ratio", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Parts to Labor Ratio", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 633.3172025240385, "y": 151.64532944164824}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 6, "datasetIndex": 0, "pointIndex": 10, "value": "1.50", "xLabel": "2023-11-01", "screenX": 908.3172025240385, "screenY": 1550.4700941261851, "canvasX": 633.3172025240385, "canvasY": 48.47009412618523, "datasetLabel": "Parts to Labor Ratio", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:22:16.534795", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$83,724.50", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $83,724.50"}}, {"item_index": 1, "title": "Total Parts Sale", "value": "$125,631.09", "html_structure": {"h5_html": "Total Parts Sale", "h6_html": " $125,631.09"}}, {"item_index": 2, "title": "Parts To Labor Ratio", "value": "$1.5", "html_structure": {"h5_html": "Parts To Labor Ratio", "h6_html": " $1.5"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$83,724.50", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $83,724.50"}}, {"item_index": 1, "title": "Total Parts Sale", "value": "$125,631.09", "html_structure": {"h5_html": "Total Parts Sale", "h6_html": " $125,631.09"}}, {"item_index": 2, "title": "Parts To Labor Ratio", "value": "$1.5", "html_structure": {"h5_html": "Parts To Labor Ratio", "h6_html": " $1.5"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$83,724.50", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $83,724.50"}}, {"item_index": 1, "title": "Total Parts Sale", "value": "$125,631.09", "html_structure": {"h5_html": "Total Parts Sale", "h6_html": " $125,631.09"}}, {"item_index": 2, "title": "Parts To Labor Ratio", "value": "$1.5", "html_structure": {"h5_html": "Parts To Labor Ratio", "h6_html": " $1.5"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Parts to Labor Ratio", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-12T10:22:16.906232", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_1316_point_0", "chart_id": "chart_1316", "chart_title": "MPI Penetration Percentage", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 10, "datasetIndex": 0, "pointIndex": 10, "value": "1.25", "xLabel": "2023-11", "screenX": 906.3926231971154, "screenY": 2433.0102823785555, "canvasX": 631.3926231971154, "canvasY": 131.01028237855562, "datasetLabel": "MPI Penetration Percentage", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "MPI Penetration Percentage", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 631.3926231971154, "y": 192.91542356783344}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1316", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 10, "datasetIndex": 0, "pointIndex": 10, "value": "1.25", "xLabel": "2023-11", "screenX": 906.3926231971154, "screenY": 2433.0102823785555, "canvasX": 631.3926231971154, "canvasY": 131.01028237855562, "datasetLabel": "MPI Penetration Percentage", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:23:26.899598", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1316", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "MPI Count", "value": "12", "html_structure": {"h5_html": "MPI Count", "h6_html": " 12"}}, {"item_index": 1, "title": "Total RO Count", "value": "926", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 926"}}, {"item_index": 2, "title": "MPI Penetration %", "value": "1.30%", "html_structure": {"h5_html": "MPI Penetration %", "h6_html": " 1.30%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "MPI Count", "value": "12", "html_structure": {"h5_html": "MPI Count", "h6_html": " 12"}}, {"item_index": 1, "title": "Total RO Count", "value": "926", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 926"}}, {"item_index": 2, "title": "MPI Penetration %", "value": "1.30%", "html_structure": {"h5_html": "MPI Penetration %", "h6_html": " 1.30%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "MPI Count", "value": "12", "html_structure": {"h5_html": "MPI Count", "h6_html": " 12"}}, {"item_index": 1, "title": "Total RO Count", "value": "926", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 926"}}, {"item_index": 2, "title": "MPI Penetration %", "value": "1.30%", "html_structure": {"h5_html": "MPI Penetration %", "h6_html": " 1.30%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "MPI Penetration Percentage", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-12T10:23:27.317835", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1316", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_1317_point_0", "chart_id": "chart_1317", "chart_title": "Menu Penetration Percentage", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 11, "datasetIndex": 0, "pointIndex": 10, "value": "1.53", "xLabel": "2023-11", "screenX": 1716.161102764423, "screenY": 2405.2767791257593, "canvasX": 632.161102764423, "canvasY": 103.27677912575918, "datasetLabel": "Menu Penetration Percentage", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Menu Penetration Percentage", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 632.161102764423, "y": 179.0486719414352}}, "navigation_result": {"success": true, "url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1317", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 11, "datasetIndex": 0, "pointIndex": 10, "value": "1.53", "xLabel": "2023-11", "screenX": 1716.161102764423, "screenY": 2405.2767791257593, "canvasX": 632.161102764423, "canvasY": 103.27677912575918, "datasetLabel": "Menu Penetration Percentage", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-12T10:24:03.766968", "page_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1317", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "<PERSON><PERSON>", "value": "9", "html_structure": {"h5_html": "<PERSON><PERSON>", "h6_html": " 9"}}, {"item_index": 1, "title": "Total RO Count", "value": "587", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 587"}}, {"item_index": 2, "title": "Menu Penetration %", "value": "1.53%", "html_structure": {"h5_html": "Menu Penetration %", "h6_html": " 1.53%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "<PERSON><PERSON>", "value": "9", "html_structure": {"h5_html": "<PERSON><PERSON>", "h6_html": " 9"}}, {"item_index": 1, "title": "Total RO Count", "value": "587", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 587"}}, {"item_index": 2, "title": "Menu Penetration %", "value": "1.53%", "html_structure": {"h5_html": "Menu Penetration %", "h6_html": " 1.53%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "<PERSON><PERSON>", "value": "9", "html_structure": {"h5_html": "<PERSON><PERSON>", "h6_html": " 9"}}, {"item_index": 1, "title": "Total RO Count", "value": "587", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 587"}}, {"item_index": 2, "title": "Menu Penetration %", "value": "1.53%", "html_structure": {"h5_html": "Menu Penetration %", "h6_html": " 1.53%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Menu Penetration Percentage", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-12T10:24:04.748154", "success": true, "legend_controlled": true, "drilldown_url": "https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1317", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}]