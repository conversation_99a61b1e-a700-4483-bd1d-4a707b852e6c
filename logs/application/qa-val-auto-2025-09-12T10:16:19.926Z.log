2025-09-12 15:46:19,928 [INFO] [root] (run_all_tests.py:39) Started running: validate_metrics
2025-09-12 15:46:19,928 [INFO] [FOPC QA AUTOMATION] (events.py:88) Start Time: 2025-09-12 15:46:19
2025-09-12 15:46:19,928 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Processing mode: Parallel (3 browsers, different charts)
2025-09-12 15:46:19,928 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Max concurrent browsers: 3
2025-09-12 15:46:19,928 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Target months/years: ['2023-11-01']
2025-09-12 15:46:19,928 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Browser timeout: 30000ms
2025-09-12 15:46:19,928 [INFO] [FOPC QA AUTOMATION] (events.py:88) ================================================================================
2025-09-12 15:46:19,929 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3437) Auth state loaded from file
2025-09-12 15:46:19,929 [INFO] [FOPC QA AUTOMATION] (events.py:88)  Valid authentication found, proceeding with processing...
2025-09-12 15:46:19,929 [INFO] [FOPC QA AUTOMATION] (events.py:88) 
 Starting parallel chart processing workflow ...
2025-09-12 15:46:19,929 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Creating chart-point combinations...
2025-09-12 15:46:41,888 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 948: CP 1-Line-RO Count
2025-09-12 15:46:41,888 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:46:41,888 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 0 for target: 2023-11-01
2025-09-12 15:46:41,898 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 0 for 2023-11-01
2025-09-12 15:46:41,899 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 0, 'datasetIndex': 0, 'pointIndex': 10, 'value': '188', 'xLabel': '2023-11-01', 'screenX': 907.0349008413461, 'screenY': 463.7152324084374, 'canvasX': 632.0349008413461, 'canvasY': 161.7152324084374, 'datasetLabel': 'Mileage Under 60k', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 0, 'datasetIndex': 1, 'pointIndex': 10, 'value': '215', 'xLabel': '2023-11-01', 'screenX': 907.0349008413461, 'screenY': 450.3437219115534, 'canvasX': 632.0349008413461, 'canvasY': 148.34372191155342, 'datasetLabel': 'Mileage Over 60k', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 0, 'datasetIndex': 2, 'pointIndex': 10, 'value': '403', 'xLabel': '2023-11-01', 'screenX': 907.0349008413461, 'screenY': 357.23838956287955, 'canvasX': 632.0349008413461, 'canvasY': 55.238389562879576, 'datasetLabel': 'Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:46:41,899 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 15:46:41,899 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 0 - 2023-11-01 (3 points)
2025-09-12 15:46:41,899 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1357: Average RO Open Days
2025-09-12 15:46:41,899 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:46:41,899 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 1 for target: 2023-11-01
2025-09-12 15:46:41,906 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 6 matching points in chart 1 for 2023-11-01
2025-09-12 15:46:41,906 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 1, 'datasetIndex': 0, 'pointIndex': 10, 'value': '3.61', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 521.0641552061844, 'canvasX': 630.7514723557692, 'canvasY': 219.06415520618438, 'datasetLabel': 'Customer Pay', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 1, 'pointIndex': 10, 'value': '1.63', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 540.6757039349476, 'canvasX': 630.7514723557692, 'canvasY': 238.6757039349476, 'datasetLabel': 'Extended Service', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 2, 'pointIndex': 10, 'value': '5.40', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 503.3345227695752, 'canvasX': 630.7514723557692, 'canvasY': 201.33452276957522, 'datasetLabel': 'Internal', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 3, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 630.7514723557692, 'canvasY': nan, 'datasetLabel': 'Maintenance', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 4, 'pointIndex': 10, 'value': '6.93', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 488.18014420644, 'canvasX': 630.7514723557692, 'canvasY': 186.18014420644, 'datasetLabel': 'Warranty', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 5, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 630.7514723557692, 'canvasY': nan, 'datasetLabel': 'Factory Service Contract', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:46:41,906 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 6 matching points for 2023-11-01
2025-09-12 15:46:41,906 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 1 - 2023-11-01 (6 points)
2025-09-12 15:46:41,906 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 923: CP 1-Line-RO Count Percentage
2025-09-12 15:46:41,906 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:46:41,906 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 2 for target: 2023-11-01
2025-09-12 15:46:41,913 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 2 for 2023-11-01
2025-09-12 15:46:41,913 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 2, 'datasetIndex': 0, 'pointIndex': 10, 'value': '70.15', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 739.6882820357193, 'canvasX': 633.44453125, 'canvasY': 37.688282035719304, 'datasetLabel': 'Mileage Under 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 2, 'datasetIndex': 1, 'pointIndex': 10, 'value': '67.40', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 748.200238949245, 'canvasX': 633.44453125, 'canvasY': 46.20023894924502, 'datasetLabel': 'Mileage Over 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 2, 'datasetIndex': 2, 'pointIndex': 10, 'value': '68.65', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 744.3311676249151, 'canvasX': 633.44453125, 'canvasY': 42.33116762491515, 'datasetLabel': 'Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:46:41,913 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 15:46:41,913 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 2 - 2023-11-01 (3 points)
2025-09-12 15:46:41,913 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1354: Multi-Line-RO Count
2025-09-12 15:46:41,913 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:46:41,913 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 3 for target: 2023-11-01
2025-09-12 15:46:41,918 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 3 for 2023-11-01
2025-09-12 15:46:41,919 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 3, 'datasetIndex': 0, 'pointIndex': 10, 'value': '80', 'xLabel': '2023-11-01', 'screenX': 1716.0349008413461, 'screenY': 877.5819840348356, 'canvasX': 632.0349008413461, 'canvasY': 175.5819840348356, 'datasetLabel': 'Mileage Under 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 3, 'datasetIndex': 1, 'pointIndex': 10, 'value': '104', 'xLabel': '2023-11-01', 'screenX': 1716.0349008413461, 'screenY': 853.810409818153, 'canvasX': 632.0349008413461, 'canvasY': 151.81040981815298, 'datasetLabel': 'Mileage Over 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 3, 'datasetIndex': 2, 'pointIndex': 10, 'value': '184', 'xLabel': '2023-11-01', 'screenX': 1716.0349008413461, 'screenY': 774.5718290958773, 'canvasX': 632.0349008413461, 'canvasY': 72.57182909587738, 'datasetLabel': 'Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:46:41,919 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 15:46:41,919 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 3 - 2023-11-01 (3 points)
2025-09-12 15:46:41,919 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1355: Multi-Line-RO Count Percentage
2025-09-12 15:46:41,919 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:46:41,919 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 4 for target: 2023-11-01
2025-09-12 15:46:41,924 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 4 for 2023-11-01
2025-09-12 15:46:41,925 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 4, 'datasetIndex': 0, 'pointIndex': 10, 'value': '29.85', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 1172.033718307117, 'canvasX': 633.44453125, 'canvasY': 70.03371830711696, 'datasetLabel': 'Mileage Under 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 4, 'datasetIndex': 1, 'pointIndex': 10, 'value': '32.60', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 1155.0098044800657, 'canvasX': 633.44453125, 'canvasY': 53.00980448006557, 'datasetLabel': 'Mileage Over 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 4, 'datasetIndex': 2, 'pointIndex': 10, 'value': '31.35', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 1162.7479471287254, 'canvasX': 633.44453125, 'canvasY': 60.7479471287253, 'datasetLabel': 'Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:46:41,925 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 15:46:41,925 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 4 - 2023-11-01 (3 points)
2025-09-12 15:46:41,925 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 938: CP Return Rate
2025-09-12 15:46:41,925 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:46:41,925 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 5 for target: 2023-11-01
2025-09-12 15:46:41,931 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 2 matching points in chart 5 for 2023-11-01
2025-09-12 15:46:41,931 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 5, 'datasetIndex': 0, 'pointIndex': 10, 'value': '8.73', 'xLabel': '2023-11', 'screenX': 1714.7514723557692, 'screenY': 1221.7125941115123, 'canvasX': 630.7514723557692, 'canvasY': 119.71259411151242, 'datasetLabel': '12 Months Return Rate', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 5, 'datasetIndex': 1, 'pointIndex': 10, 'value': '10.52', 'xLabel': '2023-11', 'screenX': 1714.7514723557692, 'screenY': 1194.0100434293106, 'canvasX': 630.7514723557692, 'canvasY': 92.01004342931061, 'datasetLabel': '6 Months Return Rate', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:46:41,931 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 2 matching points for 2023-11-01
2025-09-12 15:46:41,931 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 5 - 2023-11-01 (2 points)
2025-09-12 15:46:41,932 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 930: CP Parts to Labor Ratio
2025-09-12 15:46:41,932 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:46:41,932 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 6 for target: 2023-11-01
2025-09-12 15:46:41,937 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 1 matching points in chart 6 for 2023-11-01
2025-09-12 15:46:41,937 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 6, 'datasetIndex': 0, 'pointIndex': 10, 'value': '1.50', 'xLabel': '2023-11-01', 'screenX': 908.3172025240385, 'screenY': 1550.4700941261851, 'canvasX': 633.3172025240385, 'canvasY': 48.47009412618523, 'datasetLabel': 'Parts to Labor Ratio', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:46:41,937 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 1 matching points for 2023-11-01
2025-09-12 15:46:41,937 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 6 - 2023-11-01 (1 points)
2025-09-12 15:46:41,937 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 935: Labor Sold Hours Percentage By Pay Type
2025-09-12 15:46:41,937 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:46:41,937 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 7 for target: 2023-11-01
2025-09-12 15:46:41,944 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 6 matching points in chart 7 for 2023-11-01
2025-09-12 15:46:41,944 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 7, 'datasetIndex': 0, 'pointIndex': 10, 'value': '0.57', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1639.2007964974835, 'canvasX': 634.727959735577, 'canvasY': 137.2007964974834, 'datasetLabel': 'Customer Pay', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 1, 'pointIndex': 10, 'value': '0.05', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1746.5030412255649, 'canvasX': 634.727959735577, 'canvasY': 244.50304122556494, 'datasetLabel': 'Extended Service', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 2, 'pointIndex': 10, 'value': '0.18', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1719.6774800435446, 'canvasX': 634.727959735577, 'canvasY': 217.67748004354456, 'datasetLabel': 'Internal', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 3, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 634.727959735577, 'canvasY': nan, 'datasetLabel': 'Maintenance Plan', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 4, 'pointIndex': 10, 'value': '0.19', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1717.6139753372354, 'canvasX': 634.727959735577, 'canvasY': 215.6139753372353, 'datasetLabel': 'Warranty', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 5, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 634.727959735577, 'canvasY': nan, 'datasetLabel': 'Factory Service Contract', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:46:41,944 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 6 matching points for 2023-11-01
2025-09-12 15:46:41,944 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 7 - 2023-11-01 (6 points)
2025-09-12 15:46:41,944 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 936: CP Parts to Labor Ratio By Category
2025-09-12 15:46:41,944 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:46:41,944 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 8 for target: 2023-11-01
2025-09-12 15:46:41,950 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 8 for 2023-11-01
2025-09-12 15:46:41,950 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 8, 'datasetIndex': 0, 'pointIndex': 10, 'value': '3.37', 'xLabel': '2023-11-01', 'screenX': 906.3926231971154, 'screenY': 1948.200238949245, 'canvasX': 631.3926231971154, 'canvasY': 46.20023894924502, 'datasetLabel': 'Competitive', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 8, 'datasetIndex': 1, 'pointIndex': 10, 'value': '1.72', 'xLabel': '2023-11-01', 'screenX': 906.3926231971154, 'screenY': 2050.3437219115535, 'canvasX': 631.3926231971154, 'canvasY': 148.34372191155342, 'datasetLabel': 'Maintenance', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 8, 'datasetIndex': 2, 'pointIndex': 10, 'value': '0.95', 'xLabel': '2023-11-01', 'screenX': 906.3926231971154, 'screenY': 2098.0106806272975, 'canvasX': 631.3926231971154, 'canvasY': 196.01068062729732, 'datasetLabel': 'Repair', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:46:41,950 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 15:46:41,950 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 8 - 2023-11-01 (3 points)
2025-09-12 15:46:41,950 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1239: Revenue - Shop Supplies
2025-09-12 15:46:41,950 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:46:41,950 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 9 for target: 2023-11-01
2025-09-12 15:46:41,955 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 9 for 2023-11-01
2025-09-12 15:46:41,955 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 9, 'datasetIndex': 0, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 633.3172025240385, 'canvasY': nan, 'datasetLabel': 'Customer Pay', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 9, 'datasetIndex': 1, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 633.3172025240385, 'canvasY': nan, 'datasetLabel': 'Warranty', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 9, 'datasetIndex': 2, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 633.3172025240385, 'canvasY': nan, 'datasetLabel': 'Internal', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:46:41,956 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 15:46:41,956 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 9 - 2023-11-01 (3 points)
2025-09-12 15:46:41,956 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1316: MPI Penetration Percentage
2025-09-12 15:46:41,956 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:46:41,956 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 10 for target: 2023-11-01
2025-09-12 15:46:41,962 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 1 matching points in chart 10 for 2023-11-01
2025-09-12 15:46:41,962 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 10, 'datasetIndex': 0, 'pointIndex': 10, 'value': '1.25', 'xLabel': '2023-11', 'screenX': 906.3926231971154, 'screenY': 2433.0102823785555, 'canvasX': 631.3926231971154, 'canvasY': 131.01028237855562, 'datasetLabel': 'MPI Penetration Percentage', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:46:41,962 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 1 matching points for 2023-11-01
2025-09-12 15:46:41,962 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 10 - 2023-11-01 (1 points)
2025-09-12 15:46:41,962 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1317: Menu Penetration Percentage
2025-09-12 15:46:41,962 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:46:41,963 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 11 for target: 2023-11-01
2025-09-12 15:46:41,967 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 1 matching points in chart 11 for 2023-11-01
2025-09-12 15:46:41,967 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 11, 'datasetIndex': 0, 'pointIndex': 10, 'value': '1.53', 'xLabel': '2023-11', 'screenX': 1716.161102764423, 'screenY': 2405.2767791257593, 'canvasX': 632.161102764423, 'canvasY': 103.27677912575918, 'datasetLabel': 'Menu Penetration Percentage', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:46:41,967 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 1 matching points for 2023-11-01
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 11 - 2023-11-01 (1 points)
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 1: Average RO Open Days (6 points)
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 7: Labor Sold Hours Percentage By Pay Type (6 points)
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 0: CP 1-Line-RO Count (3 points)
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 2: CP 1-Line-RO Count Percentage (3 points)
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 3: Multi-Line-RO Count (3 points)
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 4: Multi-Line-RO Count Percentage (3 points)
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 8: CP Parts to Labor Ratio By Category (3 points)
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 9: Revenue - Shop Supplies (3 points)
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 5: CP Return Rate (2 points)
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 6: CP Parts to Labor Ratio (1 points)
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 10: MPI Penetration Percentage (1 points)
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 11: Menu Penetration Percentage (1 points)
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) 
Summary: Created 12 chart-point combinations from 12 charts
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1357: 1 combinations
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   935: 1 combinations
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   948: 1 combinations
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   923: 1 combinations
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1354: 1 combinations
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1355: 1 combinations
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   936: 1 combinations
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1239: 1 combinations
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   938: 1 combinations
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   930: 1 combinations
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1316: 1 combinations
2025-09-12 15:46:41,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1317: 1 combinations
2025-09-12 15:46:54,806 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-12 15:46:54,844 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:46:57,882 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:47:00,329 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:47:01,345 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Customer Pay in chart chart_1357
2025-09-12 15:47:03,379 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-12 15:47:04,379 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-12 15:47:04,379 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:47:04,525 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_0: Chart.js event click successful, checking for navigation...
2025-09-12 15:47:07,527 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 15:47:10,530 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1357
2025-09-12 15:47:10,531 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:47:13,534 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Customer Pay FROM DRILL DWON
2025-09-12 15:47:13,560 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:47:13,616 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:47:13,698 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:47:13,766 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:47:13,767 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 15:47:13,767 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-12 15:47:13,767 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 15:47:13,767 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_0: Data extraction successful
2025-09-12 15:47:13,767 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_0: Enhanced processing completed for 2023-11-01 from Customer Pay
2025-09-12 15:47:24,655 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:47:24,676 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:47:27,703 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:47:29,143 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:47:30,160 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Extended Service in chart chart_1357
2025-09-12 15:47:32,176 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-12 15:47:33,178 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-12 15:47:33,178 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:47:33,278 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_1: Chart.js event click successful, checking for navigation...
2025-09-12 15:47:36,279 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_1: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 15:47:39,282 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1357
2025-09-12 15:47:39,282 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:47:42,286 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Extended Service FROM DRILL DWON
2025-09-12 15:47:42,308 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:47:42,358 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:47:42,431 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:47:42,501 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:47:42,501 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 15:47:42,502 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-12 15:47:42,502 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 15:47:42,502 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_1: Data extraction successful
2025-09-12 15:47:42,502 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_1: Enhanced processing completed for 2023-11-01 from Extended Service
2025-09-12 15:47:53,403 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:47:53,429 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:47:56,440 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:47:57,869 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:47:58,906 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Internal in chart chart_1357
2025-09-12 15:48:00,934 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-12 15:48:01,935 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-12 15:48:01,935 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_2: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:48:02,068 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_2: Chart.js event click successful, checking for navigation...
2025-09-12 15:48:05,071 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_2: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 15:48:08,074 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1357
2025-09-12 15:48:08,074 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:48:11,075 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Internal FROM DRILL DWON
2025-09-12 15:48:11,083 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:48:11,133 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:48:11,208 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:48:11,277 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:48:11,277 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 15:48:11,277 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-12 15:48:11,278 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 15:48:11,278 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_2: Data extraction successful
2025-09-12 15:48:11,278 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_2: Enhanced processing completed for 2023-11-01 from Internal
2025-09-12 15:48:22,259 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:48:22,322 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:48:25,355 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:48:26,784 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:48:27,799 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Maintenance in chart chart_1357
2025-09-12 15:48:29,828 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-12 15:48:30,829 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-12 15:48:30,830 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_3: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:48:30,845 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_3: Trying coordinate-based clicking...
2025-09-12 15:48:41,759 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:48:41,815 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:48:44,830 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:48:46,243 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:48:47,266 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Warranty in chart chart_1357
2025-09-12 15:48:49,302 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-12 15:48:50,303 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-12 15:48:50,303 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_4: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:48:50,465 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_4: Chart.js event click successful, checking for navigation...
2025-09-12 15:48:53,467 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_4: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 15:48:56,469 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1357
2025-09-12 15:48:56,469 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:48:59,471 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Warranty FROM DRILL DWON
2025-09-12 15:48:59,479 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:48:59,531 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:48:59,608 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:48:59,696 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:48:59,696 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 15:48:59,696 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-12 15:48:59,696 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 15:48:59,696 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_4: Data extraction successful
2025-09-12 15:48:59,696 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_4: Enhanced processing completed for 2023-11-01 from Warranty
2025-09-12 15:49:10,676 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:49:10,700 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:49:13,714 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:49:15,170 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:49:16,185 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Factory Service Contract in chart chart_1357
2025-09-12 15:49:18,219 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-12 15:49:19,221 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-12 15:49:19,221 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_5: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:49:19,239 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_5: Trying coordinate-based clicking...
2025-09-12 15:49:26,931 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-12 15:49:27,110 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:49:30,125 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:49:32,603 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:49:33,625 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Customer Pay in chart chart_935
2025-09-12 15:49:35,647 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_935 interactivity ensured
2025-09-12 15:49:36,647 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_935
2025-09-12 15:49:36,647 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:49:36,778 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_0: Chart.js event click successful, checking for navigation...
2025-09-12 15:49:39,779 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:49:42,783 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_935
2025-09-12 15:49:42,783 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:49:45,786 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Customer Pay FROM DRILL DWON
2025-09-12 15:49:47,667 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:49:48,579 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:49:48,744 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:49:48,905 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:49:48,905 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 15:49:48,905 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 12
2025-09-12 15:49:48,905 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 15:49:48,905 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_0: Data extraction successful
2025-09-12 15:49:48,905 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_0: Enhanced processing completed for 2023-11-01 from Customer Pay
2025-09-12 15:49:59,887 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:49:59,962 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:50:02,997 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:50:04,417 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:50:05,465 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Extended Service in chart chart_935
2025-09-12 15:50:07,497 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_935 interactivity ensured
2025-09-12 15:50:08,498 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_935
2025-09-12 15:50:08,498 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:50:08,620 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_1: Chart.js event click successful, checking for navigation...
2025-09-12 15:50:11,623 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_1: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:50:14,627 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_935
2025-09-12 15:50:14,627 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:50:17,630 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Extended Service FROM DRILL DWON
2025-09-12 15:50:17,637 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 0 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:50:17,645 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:50:17,689 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:50:17,719 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:50:17,720 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: False
2025-09-12 15:50:17,720 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 0
2025-09-12 15:50:19,722 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 2/3)
2025-09-12 15:50:22,723 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Extended Service FROM DRILL DWON
2025-09-12 15:50:22,779 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:50:23,419 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:50:23,553 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:50:23,700 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:50:23,700 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 15:50:23,700 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-12 15:50:23,700 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 2
2025-09-12 15:50:23,701 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_1: Data extraction successful
2025-09-12 15:50:23,701 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_1: Enhanced processing completed for 2023-11-01 from Extended Service
2025-09-12 15:50:34,763 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:50:34,793 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:50:37,807 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:50:39,244 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:50:40,272 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Internal in chart chart_935
2025-09-12 15:50:42,313 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_935 interactivity ensured
2025-09-12 15:50:43,314 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_935
2025-09-12 15:50:43,314 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_2: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:50:43,450 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_2: Chart.js event click successful, checking for navigation...
2025-09-12 15:50:46,451 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_2: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:50:49,455 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_935
2025-09-12 15:50:49,455 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:50:52,455 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Internal FROM DRILL DWON
2025-09-12 15:50:56,097 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:50:57,850 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:50:58,008 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:50:58,156 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:50:58,156 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 15:50:58,156 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-12 15:50:58,156 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 15:50:58,156 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_2: Data extraction successful
2025-09-12 15:50:58,156 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_2: Enhanced processing completed for 2023-11-01 from Internal
2025-09-12 15:51:09,251 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:51:09,274 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:51:12,283 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:51:13,758 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:51:14,781 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Maintenance Plan in chart chart_935
2025-09-12 15:51:16,814 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_935 interactivity ensured
2025-09-12 15:51:17,815 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_935
2025-09-12 15:51:17,815 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_3: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:51:17,842 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_3: Trying coordinate-based clicking...
2025-09-12 15:51:29,227 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:51:29,290 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:51:32,299 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:51:33,753 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:51:34,778 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Warranty in chart chart_935
2025-09-12 15:51:36,805 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_935 interactivity ensured
2025-09-12 15:51:37,806 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_935
2025-09-12 15:51:37,807 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_4: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:51:37,955 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_4: Chart.js event click successful, checking for navigation...
2025-09-12 15:51:40,958 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_4: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:51:43,960 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_935
2025-09-12 15:51:43,960 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:51:46,963 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Warranty FROM DRILL DWON
2025-09-12 15:51:49,132 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:51:50,125 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:51:50,277 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:51:50,414 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:51:50,414 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 15:51:50,414 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-12 15:51:50,414 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 15:51:50,414 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_4: Data extraction successful
2025-09-12 15:51:50,414 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_4: Enhanced processing completed for 2023-11-01 from Warranty
2025-09-12 15:52:01,131 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:52:01,180 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:52:04,196 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:52:05,620 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:52:06,655 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Factory Service Contract in chart chart_935
2025-09-12 15:52:08,686 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_935 interactivity ensured
2025-09-12 15:52:09,687 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_935
2025-09-12 15:52:09,687 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_5: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:52:09,694 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_5: Trying coordinate-based clicking...
2025-09-12 15:52:17,187 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-12 15:52:17,333 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:52:20,364 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:52:22,749 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:52:23,775 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Mileage Under 60k in chart chart_948
2025-09-12 15:52:25,793 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_948 interactivity ensured
2025-09-12 15:52:26,794 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_948
2025-09-12 15:52:26,794 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_948_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:52:26,930 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_0: Chart.js event click successful, checking for navigation...
2025-09-12 15:52:29,931 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:52:32,935 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_948
2025-09-12 15:52:32,935 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:52:35,939 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Mileage Under 60k FROM DRILL DWON
2025-09-12 15:52:35,952 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:52:36,071 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:52:36,196 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:52:36,306 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:52:36,306 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 15:52:36,306 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-12 15:52:36,306 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 15:52:36,306 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_0: Data extraction successful
2025-09-12 15:52:36,306 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_0: Enhanced processing completed for 2023-11-01 from Mileage Under 60k
2025-09-12 15:52:46,993 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:52:47,022 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:52:50,051 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:52:51,433 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:52:52,461 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Mileage Over 60k in chart chart_948
2025-09-12 15:52:54,489 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_948 interactivity ensured
2025-09-12 15:52:55,491 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_948
2025-09-12 15:52:55,491 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_948_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:52:55,632 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_1: Chart.js event click successful, checking for navigation...
2025-09-12 15:52:58,636 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_1: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:53:01,639 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_948
2025-09-12 15:53:01,639 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:53:04,643 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Mileage Over 60k FROM DRILL DWON
2025-09-12 15:53:04,653 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:53:04,762 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:53:04,875 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:53:04,985 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:53:04,985 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 15:53:04,985 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-12 15:53:04,985 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 15:53:04,985 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_1: Data extraction successful
2025-09-12 15:53:04,985 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_1: Enhanced processing completed for 2023-11-01 from Mileage Over 60k
2025-09-12 15:53:15,726 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:53:15,756 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:53:18,771 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:53:20,139 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:53:21,165 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Total Shop in chart chart_948
2025-09-12 15:53:23,191 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_948 interactivity ensured
2025-09-12 15:53:24,193 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_948
2025-09-12 15:53:24,193 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_948_point_2: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:53:24,309 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_2: Chart.js event click successful, checking for navigation...
2025-09-12 15:53:27,311 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_2: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:53:30,314 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_948
2025-09-12 15:53:30,315 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:53:33,315 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Total Shop FROM DRILL DWON
2025-09-12 15:53:33,323 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:53:33,441 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:53:33,574 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:53:33,693 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:53:33,693 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 15:53:33,693 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-12 15:53:33,694 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 15:53:33,694 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_2: Data extraction successful
2025-09-12 15:53:33,694 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_2: Enhanced processing completed for 2023-11-01 from Total Shop
2025-09-12 15:53:41,899 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-12 15:53:41,933 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:53:44,948 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:53:47,336 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:53:48,356 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Mileage Under 60K in chart chart_923
2025-09-12 15:53:50,402 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_923 interactivity ensured
2025-09-12 15:53:51,403 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_923
2025-09-12 15:53:51,403 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_923_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:53:51,550 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_0: Chart.js event click successful, checking for navigation...
2025-09-12 15:53:54,551 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:53:57,555 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_923
2025-09-12 15:53:57,555 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:54:00,558 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Mileage Under 60K FROM DRILL DWON
2025-09-12 15:54:00,571 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:54:00,696 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:54:00,807 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:54:00,912 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:54:00,912 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 15:54:00,912 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 6
2025-09-12 15:54:00,912 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 15:54:00,912 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_0: Data extraction successful
2025-09-12 15:54:00,912 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_0: Enhanced processing completed for 2023-11-01 from Mileage Under 60K
2025-09-12 15:54:11,883 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:54:11,954 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:54:14,991 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:54:16,386 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:54:17,403 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Mileage Over 60K in chart chart_923
2025-09-12 15:54:19,431 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_923 interactivity ensured
2025-09-12 15:54:20,433 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_923
2025-09-12 15:54:20,433 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_923_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:54:20,546 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_1: Chart.js event click successful, checking for navigation...
2025-09-12 15:54:23,547 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_1: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:54:26,550 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_923
2025-09-12 15:54:26,550 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:54:29,551 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Mileage Over 60K FROM DRILL DWON
2025-09-12 15:54:29,558 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:54:29,634 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:54:29,729 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:54:29,814 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:54:29,815 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 15:54:29,815 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 6
2025-09-12 15:54:29,815 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 15:54:29,815 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_1: Data extraction successful
2025-09-12 15:54:29,815 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_1: Enhanced processing completed for 2023-11-01 from Mileage Over 60K
2025-09-12 15:54:40,727 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:54:40,801 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:54:43,813 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:54:45,235 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:54:46,258 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Total Shop in chart chart_923
2025-09-12 15:54:48,279 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_923 interactivity ensured
2025-09-12 15:54:49,279 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_923
2025-09-12 15:54:49,279 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_923_point_2: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:54:49,385 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_2: Chart.js event click successful, checking for navigation...
2025-09-12 15:54:52,387 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_2: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:54:55,391 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_923
2025-09-12 15:54:55,391 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:54:58,395 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Total Shop FROM DRILL DWON
2025-09-12 15:54:58,423 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:54:58,494 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:54:58,591 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:54:58,673 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:54:58,673 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 15:54:58,673 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 6
2025-09-12 15:54:58,673 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 15:54:58,673 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_2: Data extraction successful
2025-09-12 15:54:58,673 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_2: Enhanced processing completed for 2023-11-01 from Total Shop
2025-09-12 15:55:04,685 [INFO] [root] (run_all_tests.py:42) Completed: validate_metrics | Time taken: 524.76 seconds
2025-09-12 15:55:04,685 [INFO] [root] (run_all_tests.py:202) All validations completed in 532.12 seconds
2025-09-12 15:55:04,835 [INFO] [root] (report_generator.py:96) Combined HTML report created at: Final_Consolidated_Report-ginnmotorcompany/consolidated_report.html
