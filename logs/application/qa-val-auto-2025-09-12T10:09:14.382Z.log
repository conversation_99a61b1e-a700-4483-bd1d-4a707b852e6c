2025-09-12 15:39:14,384 [INFO] [root] (run_all_tests.py:39) Started running: validate_metrics
2025-09-12 15:39:14,385 [INFO] [FOPC QA AUTOMATION] (events.py:88) Start Time: 2025-09-12 15:39:14
2025-09-12 15:39:14,385 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Processing mode: Parallel (3 browsers, different charts)
2025-09-12 15:39:14,385 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Max concurrent browsers: 3
2025-09-12 15:39:14,385 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Target months/years: ['2023-11-01']
2025-09-12 15:39:14,385 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Browser timeout: 30000ms
2025-09-12 15:39:14,385 [INFO] [FOPC QA AUTOMATION] (events.py:88) ================================================================================
2025-09-12 15:39:14,385 [INFO] [FOPC QA AUTOMATION] (events.py:88) No valid authentication found. Setting up authentication...
2025-09-12 15:39:14,649 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3440) Setting up authentication...
2025-09-12 15:39:15,031 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Login attempt 1/3
2025-09-12 15:39:15,032 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Navigating to login page...
2025-09-12 15:39:18,891 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Clicking login button...
2025-09-12 15:39:19,177 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Filling username and password...
2025-09-12 15:39:20,046 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Selecting store...
2025-09-12 15:39:23,295 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Accessing dashboard...
2025-09-12 15:39:24,705 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442)  Login completed successfully
2025-09-12 15:39:24,782 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:446) Auth state saved to file
2025-09-12 15:39:24,782 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3440)  Authentication setup completed successfully
2025-09-12 15:39:25,082 [INFO] [FOPC QA AUTOMATION] (events.py:88) 
 Starting parallel chart processing workflow ...
2025-09-12 15:39:25,082 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Creating chart-point combinations...
2025-09-12 15:39:46,482 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 948: CP 1-Line-RO Count
2025-09-12 15:39:46,482 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:39:46,482 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 0 for target: 2023-11-01
2025-09-12 15:39:46,491 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 0 for 2023-11-01
2025-09-12 15:39:46,492 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 0, 'datasetIndex': 0, 'pointIndex': 10, 'value': '188', 'xLabel': '2023-11-01', 'screenX': 907.0349008413461, 'screenY': 463.7152324084374, 'canvasX': 632.0349008413461, 'canvasY': 161.7152324084374, 'datasetLabel': 'Mileage Under 60k', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 0, 'datasetIndex': 1, 'pointIndex': 10, 'value': '215', 'xLabel': '2023-11-01', 'screenX': 907.0349008413461, 'screenY': 450.3437219115534, 'canvasX': 632.0349008413461, 'canvasY': 148.34372191155342, 'datasetLabel': 'Mileage Over 60k', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 0, 'datasetIndex': 2, 'pointIndex': 10, 'value': '403', 'xLabel': '2023-11-01', 'screenX': 907.0349008413461, 'screenY': 357.23838956287955, 'canvasX': 632.0349008413461, 'canvasY': 55.238389562879576, 'datasetLabel': 'Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:39:46,492 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 15:39:46,492 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 0 - 2023-11-01 (3 points)
2025-09-12 15:39:46,492 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1357: Average RO Open Days
2025-09-12 15:39:46,492 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:39:46,492 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 1 for target: 2023-11-01
2025-09-12 15:39:46,499 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 6 matching points in chart 1 for 2023-11-01
2025-09-12 15:39:46,499 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 1, 'datasetIndex': 0, 'pointIndex': 10, 'value': '3.61', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 521.0641552061844, 'canvasX': 630.7514723557692, 'canvasY': 219.06415520618438, 'datasetLabel': 'Customer Pay', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 1, 'pointIndex': 10, 'value': '1.63', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 540.6757039349476, 'canvasX': 630.7514723557692, 'canvasY': 238.6757039349476, 'datasetLabel': 'Extended Service', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 2, 'pointIndex': 10, 'value': '5.40', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 503.3345227695752, 'canvasX': 630.7514723557692, 'canvasY': 201.33452276957522, 'datasetLabel': 'Internal', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 3, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 630.7514723557692, 'canvasY': nan, 'datasetLabel': 'Maintenance', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 4, 'pointIndex': 10, 'value': '6.93', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 488.18014420644, 'canvasX': 630.7514723557692, 'canvasY': 186.18014420644, 'datasetLabel': 'Warranty', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 5, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 630.7514723557692, 'canvasY': nan, 'datasetLabel': 'Factory Service Contract', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:39:46,499 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 6 matching points for 2023-11-01
2025-09-12 15:39:46,499 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 1 - 2023-11-01 (6 points)
2025-09-12 15:39:46,499 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 923: CP 1-Line-RO Count Percentage
2025-09-12 15:39:46,499 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:39:46,499 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 2 for target: 2023-11-01
2025-09-12 15:39:46,506 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 2 for 2023-11-01
2025-09-12 15:39:46,507 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 2, 'datasetIndex': 0, 'pointIndex': 10, 'value': '70.15', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 739.6882820357193, 'canvasX': 633.44453125, 'canvasY': 37.688282035719304, 'datasetLabel': 'Mileage Under 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 2, 'datasetIndex': 1, 'pointIndex': 10, 'value': '67.40', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 748.200238949245, 'canvasX': 633.44453125, 'canvasY': 46.20023894924502, 'datasetLabel': 'Mileage Over 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 2, 'datasetIndex': 2, 'pointIndex': 10, 'value': '68.65', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 744.3311676249151, 'canvasX': 633.44453125, 'canvasY': 42.33116762491515, 'datasetLabel': 'Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:39:46,507 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 15:39:46,507 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 2 - 2023-11-01 (3 points)
2025-09-12 15:39:46,507 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1354: Multi-Line-RO Count
2025-09-12 15:39:46,507 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:39:46,507 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 3 for target: 2023-11-01
2025-09-12 15:39:46,512 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 3 for 2023-11-01
2025-09-12 15:39:46,513 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 3, 'datasetIndex': 0, 'pointIndex': 10, 'value': '80', 'xLabel': '2023-11-01', 'screenX': 1716.0349008413461, 'screenY': 877.5819840348356, 'canvasX': 632.0349008413461, 'canvasY': 175.5819840348356, 'datasetLabel': 'Mileage Under 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 3, 'datasetIndex': 1, 'pointIndex': 10, 'value': '104', 'xLabel': '2023-11-01', 'screenX': 1716.0349008413461, 'screenY': 853.810409818153, 'canvasX': 632.0349008413461, 'canvasY': 151.81040981815298, 'datasetLabel': 'Mileage Over 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 3, 'datasetIndex': 2, 'pointIndex': 10, 'value': '184', 'xLabel': '2023-11-01', 'screenX': 1716.0349008413461, 'screenY': 774.5718290958773, 'canvasX': 632.0349008413461, 'canvasY': 72.57182909587738, 'datasetLabel': 'Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:39:46,513 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 15:39:46,513 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 3 - 2023-11-01 (3 points)
2025-09-12 15:39:46,513 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1355: Multi-Line-RO Count Percentage
2025-09-12 15:39:46,513 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:39:46,513 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 4 for target: 2023-11-01
2025-09-12 15:39:46,519 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 4 for 2023-11-01
2025-09-12 15:39:46,519 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 4, 'datasetIndex': 0, 'pointIndex': 10, 'value': '29.85', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 1172.033718307117, 'canvasX': 633.44453125, 'canvasY': 70.03371830711696, 'datasetLabel': 'Mileage Under 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 4, 'datasetIndex': 1, 'pointIndex': 10, 'value': '32.60', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 1155.0098044800657, 'canvasX': 633.44453125, 'canvasY': 53.00980448006557, 'datasetLabel': 'Mileage Over 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 4, 'datasetIndex': 2, 'pointIndex': 10, 'value': '31.35', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 1162.7479471287254, 'canvasX': 633.44453125, 'canvasY': 60.7479471287253, 'datasetLabel': 'Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:39:46,519 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 15:39:46,519 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 4 - 2023-11-01 (3 points)
2025-09-12 15:39:46,519 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 938: CP Return Rate
2025-09-12 15:39:46,519 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:39:46,519 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 5 for target: 2023-11-01
2025-09-12 15:39:46,526 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 2 matching points in chart 5 for 2023-11-01
2025-09-12 15:39:46,526 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 5, 'datasetIndex': 0, 'pointIndex': 10, 'value': '8.73', 'xLabel': '2023-11', 'screenX': 1714.7514723557692, 'screenY': 1221.7125941115123, 'canvasX': 630.7514723557692, 'canvasY': 119.71259411151242, 'datasetLabel': '12 Months Return Rate', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 5, 'datasetIndex': 1, 'pointIndex': 10, 'value': '10.52', 'xLabel': '2023-11', 'screenX': 1714.7514723557692, 'screenY': 1194.0100434293106, 'canvasX': 630.7514723557692, 'canvasY': 92.01004342931061, 'datasetLabel': '6 Months Return Rate', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:39:46,527 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 2 matching points for 2023-11-01
2025-09-12 15:39:46,527 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 5 - 2023-11-01 (2 points)
2025-09-12 15:39:46,527 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 930: CP Parts to Labor Ratio
2025-09-12 15:39:46,527 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:39:46,527 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 6 for target: 2023-11-01
2025-09-12 15:39:46,532 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 1 matching points in chart 6 for 2023-11-01
2025-09-12 15:39:46,532 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 6, 'datasetIndex': 0, 'pointIndex': 10, 'value': '1.50', 'xLabel': '2023-11-01', 'screenX': 908.3172025240385, 'screenY': 1550.4700941261851, 'canvasX': 633.3172025240385, 'canvasY': 48.47009412618523, 'datasetLabel': 'Parts to Labor Ratio', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:39:46,532 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 1 matching points for 2023-11-01
2025-09-12 15:39:46,532 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 6 - 2023-11-01 (1 points)
2025-09-12 15:39:46,532 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 935: Labor Sold Hours Percentage By Pay Type
2025-09-12 15:39:46,532 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:39:46,532 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 7 for target: 2023-11-01
2025-09-12 15:39:46,540 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 6 matching points in chart 7 for 2023-11-01
2025-09-12 15:39:46,540 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 7, 'datasetIndex': 0, 'pointIndex': 10, 'value': '0.57', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1639.2007964974835, 'canvasX': 634.727959735577, 'canvasY': 137.2007964974834, 'datasetLabel': 'Customer Pay', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 1, 'pointIndex': 10, 'value': '0.05', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1746.5030412255649, 'canvasX': 634.727959735577, 'canvasY': 244.50304122556494, 'datasetLabel': 'Extended Service', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 2, 'pointIndex': 10, 'value': '0.18', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1719.6774800435446, 'canvasX': 634.727959735577, 'canvasY': 217.67748004354456, 'datasetLabel': 'Internal', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 3, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 634.727959735577, 'canvasY': nan, 'datasetLabel': 'Maintenance Plan', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 4, 'pointIndex': 10, 'value': '0.19', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1717.6139753372354, 'canvasX': 634.727959735577, 'canvasY': 215.6139753372353, 'datasetLabel': 'Warranty', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 5, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 634.727959735577, 'canvasY': nan, 'datasetLabel': 'Factory Service Contract', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:39:46,540 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 6 matching points for 2023-11-01
2025-09-12 15:39:46,540 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 7 - 2023-11-01 (6 points)
2025-09-12 15:39:46,540 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 936: CP Parts to Labor Ratio By Category
2025-09-12 15:39:46,540 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:39:46,540 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 8 for target: 2023-11-01
2025-09-12 15:39:46,546 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 8 for 2023-11-01
2025-09-12 15:39:46,546 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 8, 'datasetIndex': 0, 'pointIndex': 10, 'value': '3.37', 'xLabel': '2023-11-01', 'screenX': 906.3926231971154, 'screenY': 1948.200238949245, 'canvasX': 631.3926231971154, 'canvasY': 46.20023894924502, 'datasetLabel': 'Competitive', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 8, 'datasetIndex': 1, 'pointIndex': 10, 'value': '1.72', 'xLabel': '2023-11-01', 'screenX': 906.3926231971154, 'screenY': 2050.3437219115535, 'canvasX': 631.3926231971154, 'canvasY': 148.34372191155342, 'datasetLabel': 'Maintenance', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 8, 'datasetIndex': 2, 'pointIndex': 10, 'value': '0.95', 'xLabel': '2023-11-01', 'screenX': 906.3926231971154, 'screenY': 2098.0106806272975, 'canvasX': 631.3926231971154, 'canvasY': 196.01068062729732, 'datasetLabel': 'Repair', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:39:46,546 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 15:39:46,546 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 8 - 2023-11-01 (3 points)
2025-09-12 15:39:46,546 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1239: Revenue - Shop Supplies
2025-09-12 15:39:46,546 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:39:46,547 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 9 for target: 2023-11-01
2025-09-12 15:39:46,552 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 9 for 2023-11-01
2025-09-12 15:39:46,552 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 9, 'datasetIndex': 0, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 633.3172025240385, 'canvasY': nan, 'datasetLabel': 'Customer Pay', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 9, 'datasetIndex': 1, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 633.3172025240385, 'canvasY': nan, 'datasetLabel': 'Warranty', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 9, 'datasetIndex': 2, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 633.3172025240385, 'canvasY': nan, 'datasetLabel': 'Internal', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:39:46,552 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 15:39:46,553 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 9 - 2023-11-01 (3 points)
2025-09-12 15:39:46,553 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1316: MPI Penetration Percentage
2025-09-12 15:39:46,553 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:39:46,553 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 10 for target: 2023-11-01
2025-09-12 15:39:46,558 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 1 matching points in chart 10 for 2023-11-01
2025-09-12 15:39:46,559 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 10, 'datasetIndex': 0, 'pointIndex': 10, 'value': '1.25', 'xLabel': '2023-11', 'screenX': 906.3926231971154, 'screenY': 2433.0102823785555, 'canvasX': 631.3926231971154, 'canvasY': 131.01028237855562, 'datasetLabel': 'MPI Penetration Percentage', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:39:46,559 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 1 matching points for 2023-11-01
2025-09-12 15:39:46,559 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 10 - 2023-11-01 (1 points)
2025-09-12 15:39:46,559 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1317: Menu Penetration Percentage
2025-09-12 15:39:46,559 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:39:46,559 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 11 for target: 2023-11-01
2025-09-12 15:39:46,564 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 1 matching points in chart 11 for 2023-11-01
2025-09-12 15:39:46,564 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 11, 'datasetIndex': 0, 'pointIndex': 10, 'value': '1.53', 'xLabel': '2023-11', 'screenX': 1716.161102764423, 'screenY': 2405.2767791257593, 'canvasX': 632.161102764423, 'canvasY': 103.27677912575918, 'datasetLabel': 'Menu Penetration Percentage', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:39:46,564 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 1 matching points for 2023-11-01
2025-09-12 15:39:46,564 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 11 - 2023-11-01 (1 points)
2025-09-12 15:39:46,564 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 1: Average RO Open Days (6 points)
2025-09-12 15:39:46,564 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 7: Labor Sold Hours Percentage By Pay Type (6 points)
2025-09-12 15:39:46,564 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 0: CP 1-Line-RO Count (3 points)
2025-09-12 15:39:46,564 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 2: CP 1-Line-RO Count Percentage (3 points)
2025-09-12 15:39:46,564 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 3: Multi-Line-RO Count (3 points)
2025-09-12 15:39:46,564 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 4: Multi-Line-RO Count Percentage (3 points)
2025-09-12 15:39:46,564 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 8: CP Parts to Labor Ratio By Category (3 points)
2025-09-12 15:39:46,564 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 9: Revenue - Shop Supplies (3 points)
2025-09-12 15:39:46,564 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 5: CP Return Rate (2 points)
2025-09-12 15:39:46,564 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 6: CP Parts to Labor Ratio (1 points)
2025-09-12 15:39:46,564 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 10: MPI Penetration Percentage (1 points)
2025-09-12 15:39:46,564 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 11: Menu Penetration Percentage (1 points)
2025-09-12 15:39:46,564 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) 
Summary: Created 12 chart-point combinations from 12 charts
2025-09-12 15:39:46,564 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1357: 1 combinations
2025-09-12 15:39:46,564 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   935: 1 combinations
2025-09-12 15:39:46,564 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   948: 1 combinations
2025-09-12 15:39:46,564 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   923: 1 combinations
2025-09-12 15:39:46,565 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1354: 1 combinations
2025-09-12 15:39:46,565 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1355: 1 combinations
2025-09-12 15:39:46,565 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   936: 1 combinations
2025-09-12 15:39:46,565 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1239: 1 combinations
2025-09-12 15:39:46,565 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   938: 1 combinations
2025-09-12 15:39:46,565 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   930: 1 combinations
2025-09-12 15:39:46,565 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1316: 1 combinations
2025-09-12 15:39:46,565 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1317: 1 combinations
2025-09-12 15:39:53,538 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-12 15:39:53,745 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:39:56,779 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:39:59,202 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:40:00,222 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Customer Pay in chart chart_1357
2025-09-12 15:40:02,252 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-12 15:40:03,253 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-12 15:40:03,253 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:40:03,409 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_0: Chart.js event click successful, checking for navigation...
2025-09-12 15:40:06,411 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 15:40:09,412 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1357
2025-09-12 15:40:09,412 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:40:12,415 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Customer Pay FROM DRILL DWON
2025-09-12 15:40:12,425 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:40:12,481 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:40:12,563 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:40:12,635 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:40:12,635 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 15:40:12,635 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-12 15:40:12,635 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 15:40:12,635 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_0: Data extraction successful
2025-09-12 15:40:12,635 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_0: Enhanced processing completed for 2023-11-01 from Customer Pay
2025-09-12 15:40:23,563 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:40:23,582 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:40:26,626 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:40:28,004 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:40:29,030 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Extended Service in chart chart_1357
2025-09-12 15:40:31,054 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-12 15:40:32,055 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-12 15:40:32,055 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:40:32,201 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_1: Chart.js event click successful, checking for navigation...
2025-09-12 15:40:35,203 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_1: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 15:40:38,204 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1357
2025-09-12 15:40:38,204 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:40:41,207 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Extended Service FROM DRILL DWON
2025-09-12 15:40:41,243 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:40:41,301 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:40:41,381 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:40:41,451 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:40:41,451 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 15:40:41,451 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-12 15:40:41,451 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 15:40:41,451 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_1: Data extraction successful
2025-09-12 15:40:41,451 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_1: Enhanced processing completed for 2023-11-01 from Extended Service
2025-09-12 15:40:52,511 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:40:52,539 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:40:55,573 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:40:57,022 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:40:58,047 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Internal in chart chart_1357
2025-09-12 15:41:00,077 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-12 15:41:01,078 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-12 15:41:01,078 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_2: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:41:01,184 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_2: Chart.js event click successful, checking for navigation...
2025-09-12 15:41:04,187 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_2: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 15:41:07,190 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1357
2025-09-12 15:41:07,191 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:41:10,193 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Internal FROM DRILL DWON
2025-09-12 15:41:10,226 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:41:10,289 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:41:10,366 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:41:10,434 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:41:10,434 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 15:41:10,435 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-12 15:41:10,435 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 15:41:10,435 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_2: Data extraction successful
2025-09-12 15:41:10,435 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_2: Enhanced processing completed for 2023-11-01 from Internal
2025-09-12 15:41:21,191 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:41:21,243 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:41:24,257 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:41:25,666 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:41:26,689 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Maintenance in chart chart_1357
2025-09-12 15:41:28,726 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-12 15:41:29,727 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-12 15:41:29,727 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_3: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:41:29,743 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_3: Trying coordinate-based clicking...
2025-09-12 15:41:40,599 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:41:40,659 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:41:43,689 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:41:45,135 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:41:46,166 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Warranty in chart chart_1357
2025-09-12 15:41:48,183 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-12 15:41:49,185 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-12 15:41:49,185 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_4: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:41:49,321 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_4: Chart.js event click successful, checking for navigation...
2025-09-12 15:41:52,323 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_4: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 15:41:55,327 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1357
2025-09-12 15:41:55,327 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:41:58,327 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Warranty FROM DRILL DWON
2025-09-12 15:41:58,339 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:41:58,390 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:41:58,463 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:41:58,532 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:41:58,532 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 15:41:58,532 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-12 15:41:58,532 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 15:41:58,532 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_4: Data extraction successful
2025-09-12 15:41:58,532 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_4: Enhanced processing completed for 2023-11-01 from Warranty
2025-09-12 15:42:09,173 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:42:09,200 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:42:12,211 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:42:13,600 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:42:14,618 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Factory Service Contract in chart chart_1357
2025-09-12 15:42:16,650 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-12 15:42:17,651 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-12 15:42:17,651 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_5: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:42:17,668 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_5: Trying coordinate-based clicking...
2025-09-12 15:42:29,564 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-12 15:42:29,608 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:42:32,620 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:42:35,042 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:42:36,063 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Customer Pay in chart chart_935
2025-09-12 15:42:38,084 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_935 interactivity ensured
2025-09-12 15:42:39,086 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_935
2025-09-12 15:42:39,086 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:42:39,235 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_0: Chart.js event click successful, checking for navigation...
2025-09-12 15:42:42,236 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:42:45,239 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_935
2025-09-12 15:42:45,239 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:42:48,240 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Customer Pay FROM DRILL DWON
2025-09-12 15:42:50,235 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:42:51,192 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:42:51,363 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:42:51,523 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:42:51,523 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 15:42:51,523 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 12
2025-09-12 15:42:51,523 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 15:42:51,523 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_0: Data extraction successful
2025-09-12 15:42:51,523 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_0: Enhanced processing completed for 2023-11-01 from Customer Pay
2025-09-12 15:43:02,199 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:43:02,218 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:43:05,232 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:43:06,635 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:43:07,659 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Extended Service in chart chart_935
2025-09-12 15:43:09,685 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_935 interactivity ensured
2025-09-12 15:43:10,686 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_935
2025-09-12 15:43:10,686 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:43:10,837 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_1: Chart.js event click successful, checking for navigation...
2025-09-12 15:43:13,840 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_1: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:43:16,841 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_935
2025-09-12 15:43:16,841 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:43:19,843 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Extended Service FROM DRILL DWON
2025-09-12 15:43:21,239 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:43:22,113 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:43:22,254 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:43:22,382 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:43:22,382 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 15:43:22,382 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-12 15:43:22,382 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 15:43:22,382 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_1: Data extraction successful
2025-09-12 15:43:22,382 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_1: Enhanced processing completed for 2023-11-01 from Extended Service
2025-09-12 15:43:33,115 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:43:33,139 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:43:36,173 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:43:37,770 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:43:38,803 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Internal in chart chart_935
2025-09-12 15:43:40,823 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_935 interactivity ensured
2025-09-12 15:43:41,823 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_935
2025-09-12 15:43:41,823 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_2: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:43:41,952 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_2: Chart.js event click successful, checking for navigation...
2025-09-12 15:43:44,955 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_2: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:43:47,958 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_935
2025-09-12 15:43:47,958 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:43:48,773 [INFO] [root] (run_all_tests.py:42) Completed: validate_metrics | Time taken: 274.39 seconds
2025-09-12 15:43:48,774 [INFO] [root] (run_all_tests.py:202) All validations completed in 283.99 seconds
2025-09-12 15:43:48,934 [INFO] [root] (report_generator.py:96) Combined HTML report created at: Final_Consolidated_Report-ginnmotorcompany/consolidated_report.html
