2025-09-12 10:06:37,974 [INFO] [root] (run_all_tests.py:39) Started running: validate_metrics
2025-09-12 10:06:37,974 [INFO] [FOPC QA AUTOMATION] (events.py:88) Start Time: 2025-09-12 10:06:37
2025-09-12 10:06:37,974 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Processing mode: Parallel (3 browsers, different charts)
2025-09-12 10:06:37,974 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Max concurrent browsers: 3
2025-09-12 10:06:37,974 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Target months/years: ['2023-11-01']
2025-09-12 10:06:37,974 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Browser timeout: 30000ms
2025-09-12 10:06:37,974 [INFO] [FOPC QA AUTOMATION] (events.py:88) ================================================================================
2025-09-12 10:06:37,975 [INFO] [FOPC QA AUTOMATION] (events.py:88) No valid authentication found. Setting up authentication...
2025-09-12 10:06:38,222 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3440) Setting up authentication...
2025-09-12 10:06:38,615 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Login attempt 1/3
2025-09-12 10:06:38,616 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Navigating to login page...
2025-09-12 10:06:42,546 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Clicking login button...
2025-09-12 10:06:42,782 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Filling username and password...
2025-09-12 10:06:43,746 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Selecting store...
2025-09-12 10:06:46,228 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Accessing dashboard...
2025-09-12 10:06:47,504 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442)  Login completed successfully
2025-09-12 10:06:47,583 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:446) Auth state saved to file
2025-09-12 10:06:47,583 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3440)  Authentication setup completed successfully
2025-09-12 10:06:47,882 [INFO] [FOPC QA AUTOMATION] (events.py:88) 
 Starting parallel chart processing workflow ...
2025-09-12 10:06:47,883 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Creating chart-point combinations...
2025-09-12 10:07:15,416 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 948: CP 1-Line-RO Count
2025-09-12 10:07:15,416 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 10:07:15,416 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 0 for target: 2023-11-01
2025-09-12 10:07:15,429 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 0 for 2023-11-01
2025-09-12 10:07:15,429 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 0, 'datasetIndex': 0, 'pointIndex': 10, 'value': '188', 'xLabel': '2023-11-01', 'screenX': 907.0349008413461, 'screenY': 463.7152324084374, 'canvasX': 632.0349008413461, 'canvasY': 161.7152324084374, 'datasetLabel': 'Mileage Under 60k', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 0, 'datasetIndex': 1, 'pointIndex': 10, 'value': '215', 'xLabel': '2023-11-01', 'screenX': 907.0349008413461, 'screenY': 450.3437219115534, 'canvasX': 632.0349008413461, 'canvasY': 148.34372191155342, 'datasetLabel': 'Mileage Over 60k', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 0, 'datasetIndex': 2, 'pointIndex': 10, 'value': '403', 'xLabel': '2023-11-01', 'screenX': 907.0349008413461, 'screenY': 357.23838956287955, 'canvasX': 632.0349008413461, 'canvasY': 55.238389562879576, 'datasetLabel': 'Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 10:07:15,429 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 10:07:15,429 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 0 - 2023-11-01 (3 points)
2025-09-12 10:07:15,429 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1357: Average RO Open Days
2025-09-12 10:07:15,429 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 10:07:15,429 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 1 for target: 2023-11-01
2025-09-12 10:07:15,441 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 6 matching points in chart 1 for 2023-11-01
2025-09-12 10:07:15,441 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 1, 'datasetIndex': 0, 'pointIndex': 10, 'value': '3.61', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 521.0641552061844, 'canvasX': 630.7514723557692, 'canvasY': 219.06415520618438, 'datasetLabel': 'Customer Pay', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 1, 'pointIndex': 10, 'value': '1.63', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 540.6757039349476, 'canvasX': 630.7514723557692, 'canvasY': 238.6757039349476, 'datasetLabel': 'Extended Service', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 2, 'pointIndex': 10, 'value': '5.40', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 503.3345227695752, 'canvasX': 630.7514723557692, 'canvasY': 201.33452276957522, 'datasetLabel': 'Internal', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 3, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 630.7514723557692, 'canvasY': nan, 'datasetLabel': 'Maintenance', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 4, 'pointIndex': 10, 'value': '6.93', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 488.18014420644, 'canvasX': 630.7514723557692, 'canvasY': 186.18014420644, 'datasetLabel': 'Warranty', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 5, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 630.7514723557692, 'canvasY': nan, 'datasetLabel': 'Factory Service Contract', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}]
2025-09-12 10:07:15,441 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 6 matching points for 2023-11-01
2025-09-12 10:07:15,441 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 1 - 2023-11-01 (6 points)
2025-09-12 10:07:15,441 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 923: CP 1-Line-RO Count Percentage
2025-09-12 10:07:15,441 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 10:07:15,441 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 2 for target: 2023-11-01
2025-09-12 10:07:15,452 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 2 for 2023-11-01
2025-09-12 10:07:15,452 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 2, 'datasetIndex': 0, 'pointIndex': 10, 'value': '70.15', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 739.6882820357193, 'canvasX': 633.44453125, 'canvasY': 37.688282035719304, 'datasetLabel': 'Mileage Under 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 2, 'datasetIndex': 1, 'pointIndex': 10, 'value': '67.40', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 748.200238949245, 'canvasX': 633.44453125, 'canvasY': 46.20023894924502, 'datasetLabel': 'Mileage Over 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 2, 'datasetIndex': 2, 'pointIndex': 10, 'value': '68.65', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 744.3311676249151, 'canvasX': 633.44453125, 'canvasY': 42.33116762491515, 'datasetLabel': 'Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 10:07:15,452 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 10:07:15,452 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 2 - 2023-11-01 (3 points)
2025-09-12 10:07:15,452 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1354: Multi-Line-RO Count
2025-09-12 10:07:15,452 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 10:07:15,452 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 3 for target: 2023-11-01
2025-09-12 10:07:15,458 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 3 for 2023-11-01
2025-09-12 10:07:15,458 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 3, 'datasetIndex': 0, 'pointIndex': 10, 'value': '80', 'xLabel': '2023-11-01', 'screenX': 1716.0349008413461, 'screenY': 877.5819840348356, 'canvasX': 632.0349008413461, 'canvasY': 175.5819840348356, 'datasetLabel': 'Mileage Under 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 3, 'datasetIndex': 1, 'pointIndex': 10, 'value': '104', 'xLabel': '2023-11-01', 'screenX': 1716.0349008413461, 'screenY': 853.810409818153, 'canvasX': 632.0349008413461, 'canvasY': 151.81040981815298, 'datasetLabel': 'Mileage Over 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 3, 'datasetIndex': 2, 'pointIndex': 10, 'value': '184', 'xLabel': '2023-11-01', 'screenX': 1716.0349008413461, 'screenY': 774.5718290958773, 'canvasX': 632.0349008413461, 'canvasY': 72.57182909587738, 'datasetLabel': 'Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 10:07:15,458 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 10:07:15,458 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 3 - 2023-11-01 (3 points)
2025-09-12 10:07:15,458 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1355: Multi-Line-RO Count Percentage
2025-09-12 10:07:15,458 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 10:07:15,458 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 4 for target: 2023-11-01
2025-09-12 10:07:15,467 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 4 for 2023-11-01
2025-09-12 10:07:15,467 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 4, 'datasetIndex': 0, 'pointIndex': 10, 'value': '29.85', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 1172.033718307117, 'canvasX': 633.44453125, 'canvasY': 70.03371830711696, 'datasetLabel': 'Mileage Under 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 4, 'datasetIndex': 1, 'pointIndex': 10, 'value': '32.60', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 1155.0098044800657, 'canvasX': 633.44453125, 'canvasY': 53.00980448006557, 'datasetLabel': 'Mileage Over 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 4, 'datasetIndex': 2, 'pointIndex': 10, 'value': '31.35', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 1162.7479471287254, 'canvasX': 633.44453125, 'canvasY': 60.7479471287253, 'datasetLabel': 'Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 10:07:15,467 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 10:07:15,467 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 4 - 2023-11-01 (3 points)
2025-09-12 10:07:15,467 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 938: CP Return Rate
2025-09-12 10:07:15,468 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 10:07:15,468 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 5 for target: 2023-11-01
2025-09-12 10:07:15,474 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 2 matching points in chart 5 for 2023-11-01
2025-09-12 10:07:15,475 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 5, 'datasetIndex': 0, 'pointIndex': 10, 'value': '8.73', 'xLabel': '2023-11', 'screenX': 1714.7514723557692, 'screenY': 1221.7125941115123, 'canvasX': 630.7514723557692, 'canvasY': 119.71259411151242, 'datasetLabel': '12 Months Return Rate', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 5, 'datasetIndex': 1, 'pointIndex': 10, 'value': '10.52', 'xLabel': '2023-11', 'screenX': 1714.7514723557692, 'screenY': 1194.0100434293106, 'canvasX': 630.7514723557692, 'canvasY': 92.01004342931061, 'datasetLabel': '6 Months Return Rate', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 10:07:15,475 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 2 matching points for 2023-11-01
2025-09-12 10:07:15,475 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 5 - 2023-11-01 (2 points)
2025-09-12 10:07:15,475 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 930: CP Parts to Labor Ratio
2025-09-12 10:07:15,475 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 10:07:15,475 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 6 for target: 2023-11-01
2025-09-12 10:07:15,483 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 1 matching points in chart 6 for 2023-11-01
2025-09-12 10:07:15,484 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 6, 'datasetIndex': 0, 'pointIndex': 10, 'value': '1.50', 'xLabel': '2023-11-01', 'screenX': 908.3172025240385, 'screenY': 1550.4700941261851, 'canvasX': 633.3172025240385, 'canvasY': 48.47009412618523, 'datasetLabel': 'Parts to Labor Ratio', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 10:07:15,484 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 1 matching points for 2023-11-01
2025-09-12 10:07:15,484 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 6 - 2023-11-01 (1 points)
2025-09-12 10:07:15,484 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 935: Labor Sold Hours Percentage By Pay Type
2025-09-12 10:07:15,484 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 10:07:15,484 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 7 for target: 2023-11-01
2025-09-12 10:07:15,494 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 6 matching points in chart 7 for 2023-11-01
2025-09-12 10:07:15,494 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 7, 'datasetIndex': 0, 'pointIndex': 10, 'value': '0.57', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1639.2007964974835, 'canvasX': 634.727959735577, 'canvasY': 137.2007964974834, 'datasetLabel': 'Customer Pay', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 1, 'pointIndex': 10, 'value': '0.05', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1746.5030412255649, 'canvasX': 634.727959735577, 'canvasY': 244.50304122556494, 'datasetLabel': 'Extended Service', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 2, 'pointIndex': 10, 'value': '0.18', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1719.6774800435446, 'canvasX': 634.727959735577, 'canvasY': 217.67748004354456, 'datasetLabel': 'Internal', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 3, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 634.727959735577, 'canvasY': nan, 'datasetLabel': 'Maintenance Plan', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 4, 'pointIndex': 10, 'value': '0.19', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1717.6139753372354, 'canvasX': 634.727959735577, 'canvasY': 215.6139753372353, 'datasetLabel': 'Warranty', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 5, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 634.727959735577, 'canvasY': nan, 'datasetLabel': 'Factory Service Contract', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}]
2025-09-12 10:07:15,494 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 6 matching points for 2023-11-01
2025-09-12 10:07:15,494 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 7 - 2023-11-01 (6 points)
2025-09-12 10:07:15,494 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 936: CP Parts to Labor Ratio By Category
2025-09-12 10:07:15,494 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 10:07:15,494 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 8 for target: 2023-11-01
2025-09-12 10:07:15,503 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 8 for 2023-11-01
2025-09-12 10:07:15,504 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 8, 'datasetIndex': 0, 'pointIndex': 10, 'value': '3.37', 'xLabel': '2023-11-01', 'screenX': 906.3926231971154, 'screenY': 1948.200238949245, 'canvasX': 631.3926231971154, 'canvasY': 46.20023894924502, 'datasetLabel': 'Competitive', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 8, 'datasetIndex': 1, 'pointIndex': 10, 'value': '1.72', 'xLabel': '2023-11-01', 'screenX': 906.3926231971154, 'screenY': 2050.3437219115535, 'canvasX': 631.3926231971154, 'canvasY': 148.34372191155342, 'datasetLabel': 'Maintenance', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 8, 'datasetIndex': 2, 'pointIndex': 10, 'value': '0.95', 'xLabel': '2023-11-01', 'screenX': 906.3926231971154, 'screenY': 2098.0106806272975, 'canvasX': 631.3926231971154, 'canvasY': 196.01068062729732, 'datasetLabel': 'Repair', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 10:07:15,504 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 10:07:15,504 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 8 - 2023-11-01 (3 points)
2025-09-12 10:07:15,504 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1239: Revenue - Shop Supplies
2025-09-12 10:07:15,504 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 10:07:15,504 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 9 for target: 2023-11-01
2025-09-12 10:07:15,510 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 9 for 2023-11-01
2025-09-12 10:07:15,510 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 9, 'datasetIndex': 0, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 633.3172025240385, 'canvasY': nan, 'datasetLabel': 'Customer Pay', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 9, 'datasetIndex': 1, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 633.3172025240385, 'canvasY': nan, 'datasetLabel': 'Warranty', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 9, 'datasetIndex': 2, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 633.3172025240385, 'canvasY': nan, 'datasetLabel': 'Internal', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}]
2025-09-12 10:07:15,510 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 10:07:15,510 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 9 - 2023-11-01 (3 points)
2025-09-12 10:07:15,510 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1316: MPI Penetration Percentage
2025-09-12 10:07:15,510 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 10:07:15,510 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 10 for target: 2023-11-01
2025-09-12 10:07:15,518 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 1 matching points in chart 10 for 2023-11-01
2025-09-12 10:07:15,518 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 10, 'datasetIndex': 0, 'pointIndex': 10, 'value': '1.25', 'xLabel': '2023-11', 'screenX': 906.3926231971154, 'screenY': 2433.0102823785555, 'canvasX': 631.3926231971154, 'canvasY': 131.01028237855562, 'datasetLabel': 'MPI Penetration Percentage', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 10:07:15,518 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 1 matching points for 2023-11-01
2025-09-12 10:07:15,518 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 10 - 2023-11-01 (1 points)
2025-09-12 10:07:15,518 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1317: Menu Penetration Percentage
2025-09-12 10:07:15,518 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 10:07:15,519 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 11 for target: 2023-11-01
2025-09-12 10:07:15,524 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 1 matching points in chart 11 for 2023-11-01
2025-09-12 10:07:15,524 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 11, 'datasetIndex': 0, 'pointIndex': 10, 'value': '1.53', 'xLabel': '2023-11', 'screenX': 1716.161102764423, 'screenY': 2405.2767791257593, 'canvasX': 632.161102764423, 'canvasY': 103.27677912575918, 'datasetLabel': 'Menu Penetration Percentage', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 10:07:15,524 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 1 matching points for 2023-11-01
2025-09-12 10:07:15,524 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 11 - 2023-11-01 (1 points)
2025-09-12 10:07:15,524 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 1: Average RO Open Days (6 points)
2025-09-12 10:07:15,524 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 7: Labor Sold Hours Percentage By Pay Type (6 points)
2025-09-12 10:07:15,524 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 0: CP 1-Line-RO Count (3 points)
2025-09-12 10:07:15,524 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 2: CP 1-Line-RO Count Percentage (3 points)
2025-09-12 10:07:15,524 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 3: Multi-Line-RO Count (3 points)
2025-09-12 10:07:15,524 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 4: Multi-Line-RO Count Percentage (3 points)
2025-09-12 10:07:15,524 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 8: CP Parts to Labor Ratio By Category (3 points)
2025-09-12 10:07:15,524 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 9: Revenue - Shop Supplies (3 points)
2025-09-12 10:07:15,525 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 5: CP Return Rate (2 points)
2025-09-12 10:07:15,525 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 6: CP Parts to Labor Ratio (1 points)
2025-09-12 10:07:15,525 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 10: MPI Penetration Percentage (1 points)
2025-09-12 10:07:15,525 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 11: Menu Penetration Percentage (1 points)
2025-09-12 10:07:15,525 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) 
Summary: Created 12 chart-point combinations from 12 charts
2025-09-12 10:07:15,525 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1357: 1 combinations
2025-09-12 10:07:15,525 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   935: 1 combinations
2025-09-12 10:07:15,525 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   948: 1 combinations
2025-09-12 10:07:15,525 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   923: 1 combinations
2025-09-12 10:07:15,525 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1354: 1 combinations
2025-09-12 10:07:15,525 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1355: 1 combinations
2025-09-12 10:07:15,525 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   936: 1 combinations
2025-09-12 10:07:15,525 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1239: 1 combinations
2025-09-12 10:07:15,525 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   938: 1 combinations
2025-09-12 10:07:15,525 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   930: 1 combinations
2025-09-12 10:07:15,525 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1316: 1 combinations
2025-09-12 10:07:15,525 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1317: 1 combinations
2025-09-12 10:07:22,829 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-12 10:07:23,029 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:07:26,062 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:07:28,470 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:07:29,506 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Customer Pay in chart chart_1357
2025-09-12 10:07:31,536 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-12 10:07:32,536 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-12 10:07:32,537 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:07:32,683 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_0: Chart.js event click successful, checking for navigation...
2025-09-12 10:07:35,686 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 10:07:38,687 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1357
2025-09-12 10:07:38,687 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:07:41,690 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Customer Pay FROM DRILL DWON
2025-09-12 10:07:41,700 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:07:41,766 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:07:41,846 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:07:41,912 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:07:41,913 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:07:41,913 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-12 10:07:41,913 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:07:41,913 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_0: Data extraction successful
2025-09-12 10:07:41,913 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_0: Enhanced processing completed for 2023-11-01 from Customer Pay
2025-09-12 10:07:52,807 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 10:07:52,827 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:07:55,857 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:07:57,276 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:07:58,306 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Extended Service in chart chart_1357
2025-09-12 10:08:00,332 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-12 10:08:01,333 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-12 10:08:01,333 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:08:01,496 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_1: Chart.js event click successful, checking for navigation...
2025-09-12 10:08:04,499 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_1: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 10:08:07,502 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1357
2025-09-12 10:08:07,503 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:08:10,507 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Extended Service FROM DRILL DWON
2025-09-12 10:08:10,538 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:08:10,599 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:08:10,679 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:08:10,769 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:08:10,769 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:08:10,770 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-12 10:08:10,770 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:08:10,770 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_1: Data extraction successful
2025-09-12 10:08:10,770 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_1: Enhanced processing completed for 2023-11-01 from Extended Service
2025-09-12 10:08:21,949 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 10:08:22,024 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:08:25,039 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:08:26,517 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:08:27,555 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Internal in chart chart_1357
2025-09-12 10:08:29,593 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-12 10:08:30,595 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-12 10:08:30,595 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_2: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:08:30,727 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_2: Chart.js event click successful, checking for navigation...
2025-09-12 10:08:33,729 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_2: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 10:08:36,732 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1357
2025-09-12 10:08:36,732 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:08:39,735 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Internal FROM DRILL DWON
2025-09-12 10:08:39,753 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:08:39,821 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:08:39,909 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:08:39,989 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:08:39,989 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:08:39,989 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-12 10:08:39,989 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:08:39,989 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_2: Data extraction successful
2025-09-12 10:08:39,989 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_2: Enhanced processing completed for 2023-11-01 from Internal
2025-09-12 10:08:55,647 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 10:08:55,671 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:08:58,686 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:09:00,088 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:09:01,117 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Maintenance in chart chart_1357
2025-09-12 10:09:03,143 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-12 10:09:04,144 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-12 10:09:04,144 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_3: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:09:04,172 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_3: Trying coordinate-based clicking...
2025-09-12 10:09:15,227 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 10:09:15,271 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:09:18,278 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:09:19,696 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:09:20,716 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Warranty in chart chart_1357
2025-09-12 10:09:22,736 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-12 10:09:23,737 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-12 10:09:23,737 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_4: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:09:23,863 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_4: Chart.js event click successful, checking for navigation...
2025-09-12 10:09:26,866 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_4: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 10:09:29,868 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1357
2025-09-12 10:09:29,868 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:09:32,871 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Warranty FROM DRILL DWON
2025-09-12 10:09:32,879 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:09:32,937 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:09:33,019 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:09:33,088 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:09:33,088 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:09:33,088 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-12 10:09:33,088 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:09:33,088 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_4: Data extraction successful
2025-09-12 10:09:33,088 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_4: Enhanced processing completed for 2023-11-01 from Warranty
2025-09-12 10:09:44,059 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 10:09:44,089 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:09:47,102 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:09:48,508 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:09:49,540 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Factory Service Contract in chart chart_1357
2025-09-12 10:09:51,562 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-12 10:09:52,563 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-12 10:09:52,563 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_5: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:09:52,571 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_5: Trying coordinate-based clicking...
2025-09-12 10:10:02,231 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-12 10:10:02,542 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:10:05,554 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:10:08,006 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:10:09,026 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Customer Pay in chart chart_935
2025-09-12 10:10:11,050 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_935 interactivity ensured
2025-09-12 10:10:12,051 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_935
2025-09-12 10:10:12,051 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:10:12,239 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_0: Chart.js event click successful, checking for navigation...
2025-09-12 10:10:15,242 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:10:18,243 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_935
2025-09-12 10:10:18,243 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:10:21,246 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Customer Pay FROM DRILL DWON
2025-09-12 10:10:23,471 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:10:24,412 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:10:24,576 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:10:24,730 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:10:24,730 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:10:24,730 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 12
2025-09-12 10:10:24,730 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:10:24,730 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_0: Data extraction successful
2025-09-12 10:10:24,731 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_0: Enhanced processing completed for 2023-11-01 from Customer Pay
2025-09-12 10:10:35,635 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 10:10:35,685 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:10:38,696 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:10:40,126 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:10:41,161 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Extended Service in chart chart_935
2025-09-12 10:10:43,188 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_935 interactivity ensured
2025-09-12 10:10:44,190 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_935
2025-09-12 10:10:44,190 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:10:44,344 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_1: Chart.js event click successful, checking for navigation...
2025-09-12 10:10:47,346 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_1: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:10:50,347 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_935
2025-09-12 10:10:50,347 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:10:53,350 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Extended Service FROM DRILL DWON
2025-09-12 10:10:54,926 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:10:55,890 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:10:56,050 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:10:56,180 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:10:56,180 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:10:56,181 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-12 10:10:56,181 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:10:56,181 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_1: Data extraction successful
2025-09-12 10:10:56,181 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_1: Enhanced processing completed for 2023-11-01 from Extended Service
2025-09-12 10:11:06,919 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 10:11:06,946 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:11:09,956 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:11:11,399 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:11:12,435 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Internal in chart chart_935
2025-09-12 10:11:14,456 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_935 interactivity ensured
2025-09-12 10:11:15,457 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_935
2025-09-12 10:11:15,457 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_2: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:11:15,587 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_2: Chart.js event click successful, checking for navigation...
2025-09-12 10:11:18,587 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_2: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:11:21,590 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_935
2025-09-12 10:11:21,590 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:11:24,591 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Internal FROM DRILL DWON
2025-09-12 10:11:26,519 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:11:27,477 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:11:27,625 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:11:27,783 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:11:27,783 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:11:27,783 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-12 10:11:27,783 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:11:27,783 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_2: Data extraction successful
2025-09-12 10:11:27,783 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_2: Enhanced processing completed for 2023-11-01 from Internal
2025-09-12 10:11:38,743 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 10:11:38,770 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:11:41,779 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:11:43,218 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:11:44,247 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Maintenance Plan in chart chart_935
2025-09-12 10:11:46,279 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_935 interactivity ensured
2025-09-12 10:11:47,280 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_935
2025-09-12 10:11:47,280 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_3: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:11:47,286 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_3: Trying coordinate-based clicking...
2025-09-12 10:11:58,562 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 10:11:58,589 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:12:01,617 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:12:03,006 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:12:04,026 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Warranty in chart chart_935
2025-09-12 10:12:06,050 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_935 interactivity ensured
2025-09-12 10:12:07,050 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_935
2025-09-12 10:12:07,050 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_4: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:12:07,195 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_4: Chart.js event click successful, checking for navigation...
2025-09-12 10:12:10,198 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_4: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:12:13,200 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_935
2025-09-12 10:12:13,200 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:12:16,203 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Warranty FROM DRILL DWON
2025-09-12 10:12:17,828 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:12:18,826 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:12:18,966 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:12:19,094 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:12:19,094 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:12:19,094 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-12 10:12:19,094 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:12:19,094 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_4: Data extraction successful
2025-09-12 10:12:19,094 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_4: Enhanced processing completed for 2023-11-01 from Warranty
2025-09-12 10:12:30,099 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 10:12:30,122 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:12:33,148 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:12:34,582 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:12:35,611 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Factory Service Contract in chart chart_935
2025-09-12 10:12:37,644 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_935 interactivity ensured
2025-09-12 10:12:38,645 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_935
2025-09-12 10:12:38,646 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_5: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:12:38,658 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_5: Trying coordinate-based clicking...
2025-09-12 10:12:45,823 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-12 10:12:46,002 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:12:49,013 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:12:51,421 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:12:52,457 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Mileage Under 60k in chart chart_948
2025-09-12 10:12:54,482 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_948 interactivity ensured
2025-09-12 10:12:55,483 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_948
2025-09-12 10:12:55,483 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_948_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:12:55,642 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_0: Chart.js event click successful, checking for navigation...
2025-09-12 10:12:58,645 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:13:01,647 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_948
2025-09-12 10:13:01,647 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:13:04,651 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Mileage Under 60k FROM DRILL DWON
2025-09-12 10:13:04,659 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:13:04,803 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:13:04,942 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:13:05,073 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:13:05,073 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:13:05,073 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-12 10:13:05,073 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:13:05,073 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_0: Data extraction successful
2025-09-12 10:13:05,073 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_0: Enhanced processing completed for 2023-11-01 from Mileage Under 60k
2025-09-12 10:13:15,739 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 10:13:15,779 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:13:18,791 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:13:20,199 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:13:21,217 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Mileage Over 60k in chart chart_948
2025-09-12 10:13:23,249 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_948 interactivity ensured
2025-09-12 10:13:24,250 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_948
2025-09-12 10:13:24,251 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_948_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:13:24,384 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_1: Chart.js event click successful, checking for navigation...
2025-09-12 10:13:27,387 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_1: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:13:30,390 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_948
2025-09-12 10:13:30,390 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:13:33,391 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Mileage Over 60k FROM DRILL DWON
2025-09-12 10:13:33,399 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:13:33,501 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:13:33,631 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:13:33,758 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:13:33,758 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:13:33,759 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-12 10:13:33,759 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:13:33,759 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_1: Data extraction successful
2025-09-12 10:13:33,759 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_1: Enhanced processing completed for 2023-11-01 from Mileage Over 60k
2025-09-12 10:13:44,647 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 10:13:44,689 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:13:47,704 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:13:49,142 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:13:50,175 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Total Shop in chart chart_948
2025-09-12 10:13:52,196 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_948 interactivity ensured
2025-09-12 10:13:53,198 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_948
2025-09-12 10:13:53,198 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_948_point_2: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:13:53,358 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_2: Chart.js event click successful, checking for navigation...
2025-09-12 10:13:56,359 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_2: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:13:59,362 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_948
2025-09-12 10:13:59,363 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:14:02,363 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Total Shop FROM DRILL DWON
2025-09-12 10:14:02,370 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:14:02,496 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:14:02,625 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:14:02,755 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:14:02,755 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:14:02,755 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-12 10:14:02,755 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:14:02,755 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_2: Data extraction successful
2025-09-12 10:14:02,755 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_2: Enhanced processing completed for 2023-11-01 from Total Shop
2025-09-12 10:14:09,866 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-12 10:14:10,357 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:14:13,372 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:14:15,788 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:14:16,809 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Mileage Under 60K in chart chart_923
2025-09-12 10:14:18,835 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_923 interactivity ensured
2025-09-12 10:14:19,837 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_923
2025-09-12 10:14:19,837 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_923_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:14:20,002 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_0: Chart.js event click successful, checking for navigation...
2025-09-12 10:14:23,004 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:14:26,008 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_923
2025-09-12 10:14:26,008 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:14:29,011 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Mileage Under 60K FROM DRILL DWON
2025-09-12 10:14:29,025 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:14:29,123 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:14:29,230 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:14:29,335 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:14:29,335 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:14:29,336 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 6
2025-09-12 10:14:29,336 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:14:29,336 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_0: Data extraction successful
2025-09-12 10:14:29,336 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_0: Enhanced processing completed for 2023-11-01 from Mileage Under 60K
2025-09-12 10:14:40,267 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 10:14:40,306 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:14:43,332 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:14:44,758 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:14:45,785 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Mileage Over 60K in chart chart_923
2025-09-12 10:14:47,812 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_923 interactivity ensured
2025-09-12 10:14:48,813 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_923
2025-09-12 10:14:48,813 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_923_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:14:48,957 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_1: Chart.js event click successful, checking for navigation...
2025-09-12 10:14:51,959 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_1: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:14:54,963 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_923
2025-09-12 10:14:54,963 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:14:57,967 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Mileage Over 60K FROM DRILL DWON
2025-09-12 10:14:57,995 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:14:58,093 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:14:58,198 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:14:58,294 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:14:58,294 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:14:58,294 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 6
2025-09-12 10:14:58,294 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:14:58,294 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_1: Data extraction successful
2025-09-12 10:14:58,294 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_1: Enhanced processing completed for 2023-11-01 from Mileage Over 60K
2025-09-12 10:15:09,307 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 10:15:09,351 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:15:12,364 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:15:13,817 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:15:14,851 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Total Shop in chart chart_923
2025-09-12 10:15:16,868 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_923 interactivity ensured
2025-09-12 10:15:17,869 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_923
2025-09-12 10:15:17,869 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_923_point_2: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:15:17,982 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_2: Chart.js event click successful, checking for navigation...
2025-09-12 10:15:20,983 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_2: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:15:23,987 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_923
2025-09-12 10:15:23,987 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:15:26,991 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Total Shop FROM DRILL DWON
2025-09-12 10:15:27,012 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:15:27,104 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:15:27,207 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:15:27,302 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:15:27,302 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:15:27,302 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 6
2025-09-12 10:15:27,302 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:15:27,302 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_2: Data extraction successful
2025-09-12 10:15:27,302 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_2: Enhanced processing completed for 2023-11-01 from Total Shop
2025-09-12 10:15:34,222 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-12 10:15:34,420 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:15:37,444 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:15:39,858 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:15:40,895 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Mileage Under 60K in chart chart_1354
2025-09-12 10:15:42,911 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1354 interactivity ensured
2025-09-12 10:15:43,911 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1354
2025-09-12 10:15:43,911 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1354_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:15:44,076 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_0: Chart.js event click successful, checking for navigation...
2025-09-12 10:15:47,079 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1354
2025-09-12 10:15:50,083 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1354
2025-09-12 10:15:50,083 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:15:53,083 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Mileage Under 60K FROM DRILL DWON
2025-09-12 10:15:53,094 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:15:53,164 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:15:53,257 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:15:53,336 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:15:53,336 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:15:53,336 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-12 10:15:53,336 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:15:53,336 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_0: Data extraction successful
2025-09-12 10:15:53,337 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_0: Enhanced processing completed for 2023-11-01 from Mileage Under 60K
2025-09-12 10:16:04,255 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 10:16:04,308 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:16:07,320 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:16:08,701 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:16:09,730 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Mileage Over 60K in chart chart_1354
2025-09-12 10:16:11,752 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1354 interactivity ensured
2025-09-12 10:16:12,754 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1354
2025-09-12 10:16:12,754 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1354_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:16:12,877 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_1: Chart.js event click successful, checking for navigation...
2025-09-12 10:16:15,879 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_1: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1354
2025-09-12 10:16:18,882 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1354
2025-09-12 10:16:18,883 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:16:21,886 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Mileage Over 60K FROM DRILL DWON
2025-09-12 10:16:21,894 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:16:21,941 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:16:22,015 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:16:22,076 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:16:22,076 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:16:22,076 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-12 10:16:22,076 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:16:22,076 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_1: Data extraction successful
2025-09-12 10:16:22,076 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_1: Enhanced processing completed for 2023-11-01 from Mileage Over 60K
2025-09-12 10:16:33,007 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 10:16:33,084 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:16:36,113 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:16:37,515 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:16:38,546 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Total Shop in chart chart_1354
2025-09-12 10:16:40,564 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1354 interactivity ensured
2025-09-12 10:16:41,565 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1354
2025-09-12 10:16:41,565 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1354_point_2: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:16:41,680 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_2: Chart.js event click successful, checking for navigation...
2025-09-12 10:16:44,683 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_2: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1354
2025-09-12 10:16:47,683 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1354
2025-09-12 10:16:47,684 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:16:50,687 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Total Shop FROM DRILL DWON
2025-09-12 10:16:50,716 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:16:50,773 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:16:50,845 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:16:50,908 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:16:50,908 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:16:50,909 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-12 10:16:50,909 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:16:50,909 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_2: Data extraction successful
2025-09-12 10:16:50,909 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_2: Enhanced processing completed for 2023-11-01 from Total Shop
2025-09-12 10:16:58,863 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-12 10:16:59,034 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:17:02,064 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:17:04,497 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:17:05,523 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Mileage Under 60K in chart chart_1355
2025-09-12 10:17:07,541 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1355 interactivity ensured
2025-09-12 10:17:08,542 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1355
2025-09-12 10:17:08,543 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1355_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:17:08,675 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_0: Chart.js event click successful, checking for navigation...
2025-09-12 10:17:11,678 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:17:14,679 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1355
2025-09-12 10:17:14,680 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:17:17,683 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Mileage Under 60K FROM DRILL DWON
2025-09-12 10:17:17,717 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:17:17,834 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:17:17,946 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:17:18,032 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:17:18,033 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:17:18,033 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 6
2025-09-12 10:17:18,033 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:17:18,033 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_0: Data extraction successful
2025-09-12 10:17:18,033 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_0: Enhanced processing completed for 2023-11-01 from Mileage Under 60K
2025-09-12 10:17:28,691 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 10:17:28,748 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:17:31,779 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:17:33,206 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:17:34,238 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Mileage Over 60K in chart chart_1355
2025-09-12 10:17:36,261 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1355 interactivity ensured
2025-09-12 10:17:37,262 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1355
2025-09-12 10:17:37,262 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1355_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:17:37,419 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_1: Chart.js event click successful, checking for navigation...
2025-09-12 10:17:40,423 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_1: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:17:43,425 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1355
2025-09-12 10:17:43,425 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:17:46,428 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Mileage Over 60K FROM DRILL DWON
2025-09-12 10:17:46,457 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:17:46,536 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:17:46,634 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:17:46,721 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:17:46,721 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:17:46,721 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 6
2025-09-12 10:17:46,721 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:17:46,721 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_1: Data extraction successful
2025-09-12 10:17:46,721 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_1: Enhanced processing completed for 2023-11-01 from Mileage Over 60K
2025-09-12 10:17:57,679 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 10:17:57,728 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:18:00,757 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:18:02,129 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:18:03,161 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Total Shop in chart chart_1355
2025-09-12 10:18:05,181 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1355 interactivity ensured
2025-09-12 10:18:06,182 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1355
2025-09-12 10:18:06,183 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1355_point_2: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:18:06,312 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_2: Chart.js event click successful, checking for navigation...
2025-09-12 10:18:09,315 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_2: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:18:12,318 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1355
2025-09-12 10:18:12,318 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:18:15,319 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Total Shop FROM DRILL DWON
2025-09-12 10:18:15,349 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:18:15,424 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:18:15,519 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:18:15,602 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:18:15,602 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:18:15,603 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 6
2025-09-12 10:18:15,603 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:18:15,603 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_2: Data extraction successful
2025-09-12 10:18:15,603 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_2: Enhanced processing completed for 2023-11-01 from Total Shop
2025-09-12 10:18:22,379 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-12 10:18:22,552 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:18:25,586 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:18:28,030 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:18:29,057 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Competitive in chart chart_936
2025-09-12 10:18:31,078 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_936 interactivity ensured
2025-09-12 10:18:32,079 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_936
2025-09-12 10:18:32,079 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_936_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:18:32,227 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_0: Chart.js event click successful, checking for navigation...
2025-09-12 10:18:35,228 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:18:38,231 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_936
2025-09-12 10:18:38,231 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:18:41,235 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Competitive FROM DRILL DWON
2025-09-12 10:18:41,268 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:18:41,383 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:18:41,501 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:18:41,617 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:18:41,617 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:18:41,617 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-12 10:18:41,618 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:18:41,618 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_0: Data extraction successful
2025-09-12 10:18:41,618 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_0: Enhanced processing completed for 2023-11-01 from Competitive
2025-09-12 10:18:52,515 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 10:18:52,574 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:18:55,602 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:18:56,977 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:18:58,000 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Maintenance in chart chart_936
2025-09-12 10:19:00,025 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_936 interactivity ensured
2025-09-12 10:19:01,026 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_936
2025-09-12 10:19:01,026 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_936_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:19:01,145 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_1: Chart.js event click successful, checking for navigation...
2025-09-12 10:19:04,148 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_1: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:19:07,151 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_936
2025-09-12 10:19:07,151 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:19:10,155 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Maintenance FROM DRILL DWON
2025-09-12 10:19:10,185 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:19:10,340 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:19:10,476 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:19:10,609 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:19:10,609 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:19:10,609 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-12 10:19:10,609 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:19:10,609 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_1: Data extraction successful
2025-09-12 10:19:10,609 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_1: Enhanced processing completed for 2023-11-01 from Maintenance
2025-09-12 10:19:21,499 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 10:19:21,560 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:19:24,586 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:19:25,982 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:19:27,014 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Repair in chart chart_936
2025-09-12 10:19:29,033 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_936 interactivity ensured
2025-09-12 10:19:30,035 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_936
2025-09-12 10:19:30,035 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_936_point_2: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:19:30,145 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_2: Chart.js event click successful, checking for navigation...
2025-09-12 10:19:33,147 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_2: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:19:36,150 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_936
2025-09-12 10:19:36,151 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:19:39,151 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Repair FROM DRILL DWON
2025-09-12 10:19:39,184 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:19:39,308 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:19:39,431 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:19:39,541 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:19:39,541 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:19:39,541 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-12 10:19:39,541 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:19:39,541 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_2: Data extraction successful
2025-09-12 10:19:39,541 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_2: Enhanced processing completed for 2023-11-01 from Repair
2025-09-12 10:19:46,806 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-12 10:19:46,986 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:19:49,997 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:19:52,450 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:19:53,474 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Customer Pay in chart chart_1239
2025-09-12 10:19:55,498 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1239 interactivity ensured
2025-09-12 10:19:56,499 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1239
2025-09-12 10:19:56,499 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1239_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:19:56,513 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1239_point_0: Trying coordinate-based clicking...
2025-09-12 10:20:07,147 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 10:20:07,177 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:20:10,213 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:20:11,598 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:20:12,632 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Warranty in chart chart_1239
2025-09-12 10:20:14,667 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1239 interactivity ensured
2025-09-12 10:20:15,668 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1239
2025-09-12 10:20:15,669 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1239_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:20:15,695 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1239_point_1: Trying coordinate-based clicking...
2025-09-12 10:20:26,697 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 10:20:26,753 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:20:29,780 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:20:31,157 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:20:32,187 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Internal in chart chart_1239
2025-09-12 10:20:34,212 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1239 interactivity ensured
2025-09-12 10:20:35,213 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1239
2025-09-12 10:20:35,214 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1239_point_2: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:20:35,258 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1239_point_2: Trying coordinate-based clicking...
2025-09-12 10:20:42,298 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-12 10:20:42,472 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:20:45,505 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:20:47,966 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:20:48,987 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for 12 Months Return Rate in chart chart_938
2025-09-12 10:20:51,012 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_938 interactivity ensured
2025-09-12 10:20:52,013 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_938
2025-09-12 10:20:52,014 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_938_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:20:52,166 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_938_point_0: Chart.js event click successful, checking for navigation...
2025-09-12 10:20:55,169 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_938_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:20:58,171 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_938
2025-09-12 10:20:58,172 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:21:01,175 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: 12 Months Return Rate FROM DRILL DWON
2025-09-12 10:21:01,203 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 0 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:21:01,222 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:21:01,269 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:21:01,300 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:21:01,300 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: False
2025-09-12 10:21:01,300 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 0
2025-09-12 10:21:03,302 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 2/3)
2025-09-12 10:21:06,306 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: 12 Months Return Rate FROM DRILL DWON
2025-09-12 10:21:06,333 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 0 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:21:06,347 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:21:06,383 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:21:06,415 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:21:06,415 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: False
2025-09-12 10:21:06,415 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 0
2025-09-12 10:21:08,415 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 3/3)
2025-09-12 10:21:11,419 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: 12 Months Return Rate FROM DRILL DWON
2025-09-12 10:21:11,440 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 0 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:21:11,459 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:21:11,500 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:21:11,531 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:21:11,531 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: False
2025-09-12 10:21:11,531 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 0
2025-09-12 10:21:11,531 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_938_point_0: Enhanced processing completed for 2023-11 from 12 Months Return Rate
2025-09-12 10:21:22,435 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 10:21:22,460 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:21:25,473 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:21:26,877 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:21:27,915 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for 6 Months Return Rate in chart chart_938
2025-09-12 10:21:29,933 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_938 interactivity ensured
2025-09-12 10:21:30,934 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_938
2025-09-12 10:21:30,934 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_938_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:21:31,078 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_938_point_1: Chart.js event click successful, checking for navigation...
2025-09-12 10:21:34,080 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_938_point_1: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:21:37,084 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_938
2025-09-12 10:21:37,084 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:21:40,086 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: 6 Months Return Rate FROM DRILL DWON
2025-09-12 10:21:40,108 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 0 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:21:40,136 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:21:40,194 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:21:40,224 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:21:40,224 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: False
2025-09-12 10:21:40,224 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 0
2025-09-12 10:21:42,226 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 2/3)
2025-09-12 10:21:45,229 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: 6 Months Return Rate FROM DRILL DWON
2025-09-12 10:21:45,252 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 0 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:21:45,270 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:21:45,306 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:21:45,335 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:21:45,336 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: False
2025-09-12 10:21:45,336 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 0
2025-09-12 10:21:47,338 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 3/3)
2025-09-12 10:21:50,339 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: 6 Months Return Rate FROM DRILL DWON
2025-09-12 10:21:50,345 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 0 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:21:50,353 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:21:50,389 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:21:50,422 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:21:50,422 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: False
2025-09-12 10:21:50,422 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 0
2025-09-12 10:21:50,422 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_938_point_1: Enhanced processing completed for 2023-11 from 6 Months Return Rate
2025-09-12 10:21:57,623 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-12 10:21:57,800 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:22:00,832 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:22:03,305 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:22:04,341 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Parts to Labor Ratio in chart chart_930
2025-09-12 10:22:06,366 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_930 interactivity ensured
2025-09-12 10:22:07,367 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_930
2025-09-12 10:22:07,367 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_930_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:22:07,527 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_930_point_0: Chart.js event click successful, checking for navigation...
2025-09-12 10:22:10,528 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_930_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:22:13,531 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_930
2025-09-12 10:22:13,531 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:22:16,534 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Parts to Labor Ratio FROM DRILL DWON
2025-09-12 10:22:16,543 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:22:16,660 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:22:16,785 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:22:16,905 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:22:16,906 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:22:16,906 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-12 10:22:16,906 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:22:16,906 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_930_point_0: Data extraction successful
2025-09-12 10:22:16,906 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_930_point_0: Enhanced processing completed for 2023-11-01 from Parts to Labor Ratio
2025-09-12 10:22:38,089 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-12 10:22:38,175 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:22:41,207 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:22:43,667 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:22:44,699 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for MPI Penetration Percentage in chart chart_1316
2025-09-12 10:22:46,722 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1316 interactivity ensured
2025-09-12 10:22:47,723 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1316
2025-09-12 10:22:47,723 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1316_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:22:47,723 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1316_point_0: Waiting for chart to load...IFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-09-12 10:23:17,891 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1316_point_0: Chart.js event click successful, checking for navigation...
2025-09-12 10:23:20,895 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1316_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1316
2025-09-12 10:23:23,895 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1316
2025-09-12 10:23:23,896 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:23:26,899 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: MPI Penetration Percentage FROM DRILL DWON
2025-09-12 10:23:26,933 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:23:27,056 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:23:27,185 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:23:27,317 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:23:27,317 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:23:27,317 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-12 10:23:27,317 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:23:27,317 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1316_point_0: Data extraction successful
2025-09-12 10:23:27,317 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1316_point_0: Enhanced processing completed for 2023-11 from MPI Penetration Percentage
2025-09-12 10:23:45,007 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-12 10:23:45,078 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:23:48,106 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 10:23:50,550 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 10:23:51,567 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Menu Penetration Percentage in chart chart_1317
2025-09-12 10:23:53,597 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1317 interactivity ensured
2025-09-12 10:23:54,598 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1317
2025-09-12 10:23:54,598 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1317_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:23:54,758 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1317_point_0: Chart.js event click successful, checking for navigation...
2025-09-12 10:23:57,759 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1317_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1317
2025-09-12 10:24:00,762 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1317
2025-09-12 10:24:00,763 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 10:24:03,767 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Menu Penetration Percentage FROM DRILL DWON
2025-09-12 10:24:04,035 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 10:24:04,485 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 10:24:04,624 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 10:24:04,747 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 10:24:04,747 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 10:24:04,748 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-12 10:24:04,748 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 10:24:04,748 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1317_point_0: Data extraction successful
2025-09-12 10:24:04,748 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1317_point_0: Enhanced processing completed for 2023-11 from Menu Penetration Percentage
2025-09-12 10:24:04,829 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3328) All results saved to chart_processing_results/chart_processing_all.json
2025-09-12 10:24:04,829 [INFO] [FOPC QA AUTOMATION] (events.py:88) 
================================================================================
2025-09-12 10:24:04,829 [INFO] [FOPC QA AUTOMATION] (events.py:88) Parallel processing with 3 browsers completed successfully!
2025-09-12 10:24:04,829 [INFO] [FOPC QA AUTOMATION] (events.py:88) Final Results:
2025-09-12 10:24:04,829 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Total tasks processed: 35
2025-09-12 10:24:04,829 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Charts processed: 12
2025-09-12 10:24:04,829 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Batches processed: 1
2025-09-12 10:24:04,829 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Successful tasks: 26
2025-09-12 10:24:04,829 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Failed tasks: 9
2025-09-12 10:24:04,829 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Success rate: 74.3%
2025-09-12 10:24:04,830 [INFO] [FOPC QA AUTOMATION] (events.py:88) ================================================================================
2025-09-12 10:24:04,830 [INFO] [FOPC QA AUTOMATION] (events.py:88)  Parallel processing completed with 26 successful extractions
2025-09-12 10:24:04,830 [INFO] [FOPC QA AUTOMATION] (events.py:88) 
================================================================================
2025-09-12 10:24:04,830 [INFO] [FOPC QA AUTOMATION] (events.py:88) GENERATING FINAL UI vs DB COMPARISON REPORT
2025-09-12 10:24:04,830 [INFO] [FOPC QA AUTOMATION] (events.py:88) ================================================================================
2025-09-12 10:24:04,830 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:342) Target month range: 2023-11-01 to 2023-11-30
2025-09-12 10:24:04,830 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:342) Fetching data from database...
2025-09-12 10:24:40,378 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:284) Target month data shape: (2682, 48)
2025-09-12 10:24:42,642 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473) 
================================================================================
2025-09-12 10:24:42,642 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473) RESULTS PROCESSING
2025-09-12 10:24:42,642 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473) ================================================================================
2025-09-12 10:24:42,642 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473) 
Target month Special Metrics data written successfully to chart_processing_results/db_calculated_value.json
2025-09-12 10:24:42,642 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473) 
Target Month Summary for November 2023:
2025-09-12 10:24:42,642 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473)   Total Revenue: $209,355.59
2025-09-12 10:24:42,642 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473)   Total Gross Profit: $106,275.19
2025-09-12 10:24:42,642 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473)   GP Percentage: 50.8%
2025-09-12 10:24:42,642 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473)   Total ROs: 987
2025-09-12 10:24:42,642 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473)     - Customer Pay ROs: 587
2025-09-12 10:24:42,642 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473)     - Warranty ROs: 274
2025-09-12 10:24:42,643 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473)     - Internal ROs: 126
2025-09-12 10:24:42,643 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473)   Labor sold hours: 644.2
2025-09-12 10:24:42,643 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473) 
================================================================================
2025-09-12 10:24:42,643 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473) Special Metrics ANALYSIS - MAIN EXECUTION COMPLETED
2025-09-12 10:24:42,643 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473) ================================================================================
2025-09-12 10:24:42,643 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3477) Starting CP Overview comparison...
2025-09-12 10:24:42,643 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3477) Loading UI data from: chart_processing_results/chart_processing_all.json
2025-09-12 10:24:42,643 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3477) Loading DB data from: chart_processing_results/db_calculated_value.json
2025-09-12 10:24:42,644 [INFO] [FOPC QA AUTOMATION] (compare_cp_overview.py:463) Processing 35 UI charts
2025-09-12 10:24:42,644 [INFO] [FOPC QA AUTOMATION] (compare_cp_overview.py:463) Extracted 17 UI chart values
2025-09-12 10:24:42,645 [INFO] [FOPC QA AUTOMATION] (compare_cp_overview.py:464) Target month: 2023-11-01, Processing DB values for date: 2023-11-01
2025-09-12 10:24:42,645 [INFO] [FOPC QA AUTOMATION] (compare_cp_overview.py:464) Extracted 12 DB values from CP overview data
2025-09-12 10:24:42,645 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3477) CSV comparison results saved to Individual_Reports-ginnmotorcompany/cp_overview_comparison_results.csv
2025-09-12 10:24:42,658 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3477) Excel file with highlighted mismatches saved as Individual_Reports-ginnmotorcompany/cp_overview_comparison_highlighted.xlsx
2025-09-12 10:24:42,659 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3477) JSON report saved to Individual_Reports-ginnmotorcompany/cp_overview_comparison.json
2025-09-12 10:24:42,660 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3477) HTML report saved to Individual_Reports-ginnmotorcompany/cp_overview_comparison.html
2025-09-12 10:24:42,660 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3477) CP Overview comparison completed successfully
2025-09-12 10:24:42,660 [INFO] [FOPC QA AUTOMATION] (events.py:88) End Time: 1084.6859374046326
2025-09-12 10:24:42,660 [INFO] [root] (run_all_tests.py:42) Completed: validate_metrics | Time taken: 1084.69 seconds
2025-09-12 10:24:42,660 [INFO] [root] (run_all_tests.py:202) All validations completed in 1096.74 seconds
2025-09-12 10:24:42,829 [INFO] [root] (report_generator.py:96) Combined HTML report created at: Final_Consolidated_Report-ginnmotorcompany/consolidated_report.html
