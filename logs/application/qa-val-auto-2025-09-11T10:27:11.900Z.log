2025-09-11 15:57:11,902 [INFO] [root] (run_all_tests.py:39) Started running: validate_metrics
2025-09-11 15:57:11,902 [INFO] [FOPC QA AUTOMATION] (events.py:88) Start Time: 2025-09-11 15:57:11
2025-09-11 15:57:11,902 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Processing mode: Parallel (3 browsers, different charts)
2025-09-11 15:57:11,902 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Max concurrent browsers: 3
2025-09-11 15:57:11,902 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Target months/years: ['2023-11-01']
2025-09-11 15:57:11,902 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Browser timeout: 30000ms
2025-09-11 15:57:11,902 [INFO] [FOPC QA AUTOMATION] (events.py:88) ================================================================================
2025-09-11 15:57:11,902 [INFO] [FOPC QA AUTOMATION] (events.py:88) No valid authentication found. Setting up authentication...
2025-09-11 15:57:12,155 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3440) Setting up authentication...
2025-09-11 15:57:12,521 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Login attempt 1/3
2025-09-11 15:57:12,521 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Navigating to login page...
2025-09-11 15:57:16,299 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Clicking login button...
2025-09-11 15:57:16,520 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Filling username and password...
2025-09-11 15:57:17,389 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Selecting store...
2025-09-11 15:57:20,110 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Accessing dashboard...
2025-09-11 15:57:21,493 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442)  Login completed successfully
2025-09-11 15:57:21,565 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:446) Auth state saved to file
2025-09-11 15:57:21,566 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3440)  Authentication setup completed successfully
2025-09-11 15:57:21,854 [INFO] [FOPC QA AUTOMATION] (events.py:88) 
 Starting parallel chart processing workflow ...
2025-09-11 15:57:21,855 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Creating chart-point combinations...
2025-09-11 15:57:43,182 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 948: CP 1-Line-RO Count
2025-09-11 15:57:43,182 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-11 15:57:43,182 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 0 for target: 2023-11-01
2025-09-11 15:57:43,193 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 0 for 2023-11-01
2025-09-11 15:57:43,193 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 0, 'datasetIndex': 0, 'pointIndex': 10, 'value': '188', 'xLabel': '2023-11-01', 'screenX': 907.0349008413461, 'screenY': 463.7152324084374, 'canvasX': 632.0349008413461, 'canvasY': 161.7152324084374, 'datasetLabel': 'Mileage Under 60k', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 0, 'datasetIndex': 1, 'pointIndex': 10, 'value': '215', 'xLabel': '2023-11-01', 'screenX': 907.0349008413461, 'screenY': 450.3437219115534, 'canvasX': 632.0349008413461, 'canvasY': 148.34372191155342, 'datasetLabel': 'Mileage Over 60k', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 0, 'datasetIndex': 2, 'pointIndex': 10, 'value': '403', 'xLabel': '2023-11-01', 'screenX': 907.0349008413461, 'screenY': 357.23838956287955, 'canvasX': 632.0349008413461, 'canvasY': 55.238389562879576, 'datasetLabel': 'Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-11 15:57:43,193 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-11 15:57:43,193 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 0 - 2023-11-01 (3 points)
2025-09-11 15:57:43,193 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1357: Average RO Open Days
2025-09-11 15:57:43,193 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-11 15:57:43,193 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 1 for target: 2023-11-01
2025-09-11 15:57:43,201 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 6 matching points in chart 1 for 2023-11-01
2025-09-11 15:57:43,201 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 1, 'datasetIndex': 0, 'pointIndex': 10, 'value': '3.61', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 521.0641552061844, 'canvasX': 630.7514723557692, 'canvasY': 219.06415520618438, 'datasetLabel': 'Customer Pay', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 1, 'pointIndex': 10, 'value': '1.63', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 540.6757039349476, 'canvasX': 630.7514723557692, 'canvasY': 238.6757039349476, 'datasetLabel': 'Extended Service', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 2, 'pointIndex': 10, 'value': '5.40', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 503.3345227695752, 'canvasX': 630.7514723557692, 'canvasY': 201.33452276957522, 'datasetLabel': 'Internal', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 3, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 630.7514723557692, 'canvasY': nan, 'datasetLabel': 'Maintenance', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 4, 'pointIndex': 10, 'value': '6.93', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 488.18014420644, 'canvasX': 630.7514723557692, 'canvasY': 186.18014420644, 'datasetLabel': 'Warranty', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 5, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 630.7514723557692, 'canvasY': nan, 'datasetLabel': 'Factory Service Contract', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}]
2025-09-11 15:57:43,201 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 6 matching points for 2023-11-01
2025-09-11 15:57:43,201 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 1 - 2023-11-01 (6 points)
2025-09-11 15:57:43,201 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 923: CP 1-Line-RO Count Percentage
2025-09-11 15:57:43,201 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-11 15:57:43,201 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 2 for target: 2023-11-01
2025-09-11 15:57:43,209 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 2 for 2023-11-01
2025-09-11 15:57:43,209 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 2, 'datasetIndex': 0, 'pointIndex': 10, 'value': '70.15', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 739.6882820357193, 'canvasX': 633.44453125, 'canvasY': 37.688282035719304, 'datasetLabel': 'Mileage Under 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 2, 'datasetIndex': 1, 'pointIndex': 10, 'value': '67.40', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 748.200238949245, 'canvasX': 633.44453125, 'canvasY': 46.20023894924502, 'datasetLabel': 'Mileage Over 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 2, 'datasetIndex': 2, 'pointIndex': 10, 'value': '68.65', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 744.3311676249151, 'canvasX': 633.44453125, 'canvasY': 42.33116762491515, 'datasetLabel': 'Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-11 15:57:43,209 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-11 15:57:43,209 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 2 - 2023-11-01 (3 points)
2025-09-11 15:57:43,209 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1354: Multi-Line-RO Count
2025-09-11 15:57:43,209 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-11 15:57:43,210 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 3 for target: 2023-11-01
2025-09-11 15:57:43,216 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 3 for 2023-11-01
2025-09-11 15:57:43,216 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 3, 'datasetIndex': 0, 'pointIndex': 10, 'value': '80', 'xLabel': '2023-11-01', 'screenX': 1716.0349008413461, 'screenY': 877.5819840348356, 'canvasX': 632.0349008413461, 'canvasY': 175.5819840348356, 'datasetLabel': 'Mileage Under 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 3, 'datasetIndex': 1, 'pointIndex': 10, 'value': '104', 'xLabel': '2023-11-01', 'screenX': 1716.0349008413461, 'screenY': 853.810409818153, 'canvasX': 632.0349008413461, 'canvasY': 151.81040981815298, 'datasetLabel': 'Mileage Over 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 3, 'datasetIndex': 2, 'pointIndex': 10, 'value': '184', 'xLabel': '2023-11-01', 'screenX': 1716.0349008413461, 'screenY': 774.5718290958773, 'canvasX': 632.0349008413461, 'canvasY': 72.57182909587738, 'datasetLabel': 'Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-11 15:57:43,216 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-11 15:57:43,216 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 3 - 2023-11-01 (3 points)
2025-09-11 15:57:43,216 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1355: Multi-Line-RO Count Percentage
2025-09-11 15:57:43,216 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-11 15:57:43,216 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 4 for target: 2023-11-01
2025-09-11 15:57:43,223 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 4 for 2023-11-01
2025-09-11 15:57:43,223 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 4, 'datasetIndex': 0, 'pointIndex': 10, 'value': '29.85', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 1172.033718307117, 'canvasX': 633.44453125, 'canvasY': 70.03371830711696, 'datasetLabel': 'Mileage Under 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 4, 'datasetIndex': 1, 'pointIndex': 10, 'value': '32.60', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 1155.0098044800657, 'canvasX': 633.44453125, 'canvasY': 53.00980448006557, 'datasetLabel': 'Mileage Over 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 4, 'datasetIndex': 2, 'pointIndex': 10, 'value': '31.35', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 1162.7479471287254, 'canvasX': 633.44453125, 'canvasY': 60.7479471287253, 'datasetLabel': 'Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-11 15:57:43,223 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-11 15:57:43,223 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 4 - 2023-11-01 (3 points)
2025-09-11 15:57:43,223 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 938: CP Return Rate
2025-09-11 15:57:43,223 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-11 15:57:43,223 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 5 for target: 2023-11-01
2025-09-11 15:57:43,229 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 2 matching points in chart 5 for 2023-11-01
2025-09-11 15:57:43,229 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 5, 'datasetIndex': 0, 'pointIndex': 10, 'value': '8.73', 'xLabel': '2023-11', 'screenX': 1714.7514723557692, 'screenY': 1221.7125941115123, 'canvasX': 630.7514723557692, 'canvasY': 119.71259411151242, 'datasetLabel': '12 Months Return Rate', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 5, 'datasetIndex': 1, 'pointIndex': 10, 'value': '10.52', 'xLabel': '2023-11', 'screenX': 1714.7514723557692, 'screenY': 1194.0100434293106, 'canvasX': 630.7514723557692, 'canvasY': 92.01004342931061, 'datasetLabel': '6 Months Return Rate', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-11 15:57:43,229 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 2 matching points for 2023-11-01
2025-09-11 15:57:43,229 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 5 - 2023-11-01 (2 points)
2025-09-11 15:57:43,229 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 930: CP Parts to Labor Ratio
2025-09-11 15:57:43,229 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-11 15:57:43,230 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 6 for target: 2023-11-01
2025-09-11 15:57:43,234 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 1 matching points in chart 6 for 2023-11-01
2025-09-11 15:57:43,235 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 6, 'datasetIndex': 0, 'pointIndex': 10, 'value': '1.50', 'xLabel': '2023-11-01', 'screenX': 908.3172025240385, 'screenY': 1550.4700941261851, 'canvasX': 633.3172025240385, 'canvasY': 48.47009412618523, 'datasetLabel': 'Parts to Labor Ratio', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-11 15:57:43,235 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 1 matching points for 2023-11-01
2025-09-11 15:57:43,235 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 6 - 2023-11-01 (1 points)
2025-09-11 15:57:43,235 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 935: Labor Sold Hours Percentage By Pay Type
2025-09-11 15:57:43,235 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-11 15:57:43,235 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 7 for target: 2023-11-01
2025-09-11 15:57:43,242 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 6 matching points in chart 7 for 2023-11-01
2025-09-11 15:57:43,242 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 7, 'datasetIndex': 0, 'pointIndex': 10, 'value': '0.57', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1639.2007964974835, 'canvasX': 634.727959735577, 'canvasY': 137.2007964974834, 'datasetLabel': 'Customer Pay', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 1, 'pointIndex': 10, 'value': '0.05', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1746.5030412255649, 'canvasX': 634.727959735577, 'canvasY': 244.50304122556494, 'datasetLabel': 'Extended Service', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 2, 'pointIndex': 10, 'value': '0.18', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1719.6774800435446, 'canvasX': 634.727959735577, 'canvasY': 217.67748004354456, 'datasetLabel': 'Internal', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 3, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 634.727959735577, 'canvasY': nan, 'datasetLabel': 'Maintenance Plan', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 4, 'pointIndex': 10, 'value': '0.19', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1717.6139753372354, 'canvasX': 634.727959735577, 'canvasY': 215.6139753372353, 'datasetLabel': 'Warranty', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 5, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 634.727959735577, 'canvasY': nan, 'datasetLabel': 'Factory Service Contract', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}]
2025-09-11 15:57:43,242 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 6 matching points for 2023-11-01
2025-09-11 15:57:43,242 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 7 - 2023-11-01 (6 points)
2025-09-11 15:57:43,242 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 936: CP Parts to Labor Ratio By Category
2025-09-11 15:57:43,242 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-11 15:57:43,242 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 8 for target: 2023-11-01
2025-09-11 15:57:43,248 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 8 for 2023-11-01
2025-09-11 15:57:43,248 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 8, 'datasetIndex': 0, 'pointIndex': 10, 'value': '3.37', 'xLabel': '2023-11-01', 'screenX': 906.3926231971154, 'screenY': 1948.200238949245, 'canvasX': 631.3926231971154, 'canvasY': 46.20023894924502, 'datasetLabel': 'Competitive', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 8, 'datasetIndex': 1, 'pointIndex': 10, 'value': '1.72', 'xLabel': '2023-11-01', 'screenX': 906.3926231971154, 'screenY': 2050.3437219115535, 'canvasX': 631.3926231971154, 'canvasY': 148.34372191155342, 'datasetLabel': 'Maintenance', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 8, 'datasetIndex': 2, 'pointIndex': 10, 'value': '0.95', 'xLabel': '2023-11-01', 'screenX': 906.3926231971154, 'screenY': 2098.0106806272975, 'canvasX': 631.3926231971154, 'canvasY': 196.01068062729732, 'datasetLabel': 'Repair', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-11 15:57:43,248 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-11 15:57:43,248 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 8 - 2023-11-01 (3 points)
2025-09-11 15:57:43,248 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1239: Revenue - Shop Supplies
2025-09-11 15:57:43,248 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-11 15:57:43,248 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 9 for target: 2023-11-01
2025-09-11 15:57:43,254 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 9 for 2023-11-01
2025-09-11 15:57:43,255 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 9, 'datasetIndex': 0, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 633.3172025240385, 'canvasY': nan, 'datasetLabel': 'Customer Pay', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 9, 'datasetIndex': 1, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 633.3172025240385, 'canvasY': nan, 'datasetLabel': 'Warranty', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 9, 'datasetIndex': 2, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 633.3172025240385, 'canvasY': nan, 'datasetLabel': 'Internal', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}]
2025-09-11 15:57:43,255 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-11 15:57:43,255 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 9 - 2023-11-01 (3 points)
2025-09-11 15:57:43,255 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1316: MPI Penetration Percentage
2025-09-11 15:57:43,255 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-11 15:57:43,255 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 10 for target: 2023-11-01
2025-09-11 15:57:43,260 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 1 matching points in chart 10 for 2023-11-01
2025-09-11 15:57:43,260 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 10, 'datasetIndex': 0, 'pointIndex': 10, 'value': '1.25', 'xLabel': '2023-11', 'screenX': 906.3926231971154, 'screenY': 2433.0102823785555, 'canvasX': 631.3926231971154, 'canvasY': 131.01028237855562, 'datasetLabel': 'MPI Penetration Percentage', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-11 15:57:43,260 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 1 matching points for 2023-11-01
2025-09-11 15:57:43,260 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 10 - 2023-11-01 (1 points)
2025-09-11 15:57:43,260 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1317: Menu Penetration Percentage
2025-09-11 15:57:43,260 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-11 15:57:43,260 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 11 for target: 2023-11-01
2025-09-11 15:57:43,265 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 1 matching points in chart 11 for 2023-11-01
2025-09-11 15:57:43,265 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 11, 'datasetIndex': 0, 'pointIndex': 10, 'value': '1.53', 'xLabel': '2023-11', 'screenX': 1716.161102764423, 'screenY': 2405.2767791257593, 'canvasX': 632.161102764423, 'canvasY': 103.27677912575918, 'datasetLabel': 'Menu Penetration Percentage', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-11 15:57:43,265 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 1 matching points for 2023-11-01
2025-09-11 15:57:43,265 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 11 - 2023-11-01 (1 points)
2025-09-11 15:57:43,265 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 1: Average RO Open Days (6 points)
2025-09-11 15:57:43,265 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 7: Labor Sold Hours Percentage By Pay Type (6 points)
2025-09-11 15:57:43,265 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 0: CP 1-Line-RO Count (3 points)
2025-09-11 15:57:43,265 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 2: CP 1-Line-RO Count Percentage (3 points)
2025-09-11 15:57:43,266 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 3: Multi-Line-RO Count (3 points)
2025-09-11 15:57:43,266 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 4: Multi-Line-RO Count Percentage (3 points)
2025-09-11 15:57:43,266 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 8: CP Parts to Labor Ratio By Category (3 points)
2025-09-11 15:57:43,266 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 9: Revenue - Shop Supplies (3 points)
2025-09-11 15:57:43,266 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 5: CP Return Rate (2 points)
2025-09-11 15:57:43,266 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 6: CP Parts to Labor Ratio (1 points)
2025-09-11 15:57:43,266 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 10: MPI Penetration Percentage (1 points)
2025-09-11 15:57:43,266 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 11: Menu Penetration Percentage (1 points)
2025-09-11 15:57:43,266 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) 
Summary: Created 12 chart-point combinations from 12 charts
2025-09-11 15:57:43,266 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1357: 1 combinations
2025-09-11 15:57:43,266 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   935: 1 combinations
2025-09-11 15:57:43,266 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   948: 1 combinations
2025-09-11 15:57:43,266 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   923: 1 combinations
2025-09-11 15:57:43,266 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1354: 1 combinations
2025-09-11 15:57:43,266 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1355: 1 combinations
2025-09-11 15:57:43,266 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   936: 1 combinations
2025-09-11 15:57:43,266 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1239: 1 combinations
2025-09-11 15:57:43,266 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   938: 1 combinations
2025-09-11 15:57:43,266 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   930: 1 combinations
2025-09-11 15:57:43,266 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1316: 1 combinations
2025-09-11 15:57:43,266 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1317: 1 combinations
2025-09-11 15:57:58,863 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-11 15:58:01,941 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 15:58:04,953 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 15:58:07,384 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 15:58:08,403 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Customer Pay in chart chart_1357
2025-09-11 15:58:10,426 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-11 15:58:11,427 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-11 15:58:11,427 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 15:58:11,574 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_0: Chart.js event click successful, checking for navigation...
2025-09-11 15:58:14,575 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-11 15:58:17,578 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1357
2025-09-11 15:58:17,579 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 15:58:20,579 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Customer Pay FROM DRILL DWON
2025-09-11 15:58:20,587 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 15:58:20,638 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 15:58:20,709 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 15:58:20,772 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 15:58:20,772 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 15:58:20,772 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-11 15:58:20,772 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 15:58:20,772 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_0: Data extraction successful
2025-09-11 15:58:20,772 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_0: Enhanced processing completed for 2023-11-01 from Customer Pay
2025-09-11 15:58:31,667 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-11 15:58:31,736 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 15:58:34,770 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 15:58:36,196 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 15:58:37,215 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Extended Service in chart chart_1357
2025-09-11 15:58:39,238 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-11 15:58:40,239 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-11 15:58:40,239 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 15:58:40,354 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_1: Chart.js event click successful, checking for navigation...
2025-09-11 15:58:43,355 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_1: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-11 15:58:46,359 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1357
2025-09-11 15:58:46,359 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 15:58:49,359 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Extended Service FROM DRILL DWON
2025-09-11 15:58:49,391 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 15:58:49,445 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 15:58:49,522 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 15:58:49,591 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 15:58:49,591 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 15:58:49,591 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-11 15:58:49,591 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 15:58:49,591 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_1: Data extraction successful
2025-09-11 15:58:49,592 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_1: Enhanced processing completed for 2023-11-01 from Extended Service
2025-09-11 15:59:00,467 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-11 15:59:00,523 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 15:59:03,540 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 15:59:04,921 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 15:59:05,955 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Internal in chart chart_1357
2025-09-11 15:59:07,984 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-11 15:59:08,985 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-11 15:59:08,985 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_2: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 15:59:09,126 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_2: Chart.js event click successful, checking for navigation...
2025-09-11 15:59:12,129 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_2: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-11 15:59:15,131 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1357
2025-09-11 15:59:15,131 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 15:59:18,133 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Internal FROM DRILL DWON
2025-09-11 15:59:18,162 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 15:59:18,226 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 15:59:18,298 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 15:59:18,363 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 15:59:18,363 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 15:59:18,363 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-11 15:59:18,363 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 15:59:18,363 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_2: Data extraction successful
2025-09-11 15:59:18,363 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_2: Enhanced processing completed for 2023-11-01 from Internal
2025-09-11 15:59:29,361 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-11 15:59:29,427 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 15:59:32,459 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 15:59:33,855 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 15:59:34,872 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Maintenance in chart chart_1357
2025-09-11 15:59:36,889 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-11 15:59:37,890 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-11 15:59:37,890 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_3: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 15:59:37,910 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_3: Trying coordinate-based clicking...
2025-09-11 15:59:48,843 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-11 15:59:48,863 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 15:59:51,874 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 15:59:53,294 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 15:59:54,327 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Warranty in chart chart_1357
2025-09-11 15:59:56,360 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-11 15:59:57,361 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-11 15:59:57,361 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_4: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 15:59:57,487 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_4: Chart.js event click successful, checking for navigation...
2025-09-11 16:00:00,490 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_4: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-11 16:00:03,491 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1357
2025-09-11 16:00:03,492 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:00:06,495 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Warranty FROM DRILL DWON
2025-09-11 16:00:06,529 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:00:06,582 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:00:06,654 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:00:06,715 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:00:06,715 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 16:00:06,715 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-11 16:00:06,715 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 16:00:06,715 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_4: Data extraction successful
2025-09-11 16:00:06,715 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_4: Enhanced processing completed for 2023-11-01 from Warranty
2025-09-11 16:00:17,679 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-11 16:00:17,746 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:00:20,775 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:00:22,187 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:00:23,205 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Factory Service Contract in chart chart_1357
2025-09-11 16:00:25,234 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-11 16:00:26,235 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-11 16:00:26,235 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_5: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:00:26,254 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_5: Trying coordinate-based clicking...
2025-09-11 16:00:33,354 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-11 16:00:33,528 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:00:36,560 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:00:38,939 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:00:39,964 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Customer Pay in chart chart_935
2025-09-11 16:00:41,983 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_935 interactivity ensured
2025-09-11 16:00:42,984 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_935
2025-09-11 16:00:42,985 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:00:43,129 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_0: Chart.js event click successful, checking for navigation...
2025-09-11 16:00:46,131 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:00:49,135 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_935
2025-09-11 16:00:49,135 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:00:52,138 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Customer Pay FROM DRILL DWON
2025-09-11 16:00:53,860 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:00:54,759 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:00:54,906 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:00:55,044 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:00:55,044 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 16:00:55,044 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 12
2025-09-11 16:00:55,044 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 16:00:55,044 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_0: Data extraction successful
2025-09-11 16:00:55,044 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_0: Enhanced processing completed for 2023-11-01 from Customer Pay
2025-09-11 16:01:05,915 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-11 16:01:05,943 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:01:08,959 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:01:10,374 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:01:11,391 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Extended Service in chart chart_935
2025-09-11 16:01:13,421 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_935 interactivity ensured
2025-09-11 16:01:14,422 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_935
2025-09-11 16:01:14,423 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:01:14,526 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_1: Chart.js event click successful, checking for navigation...
2025-09-11 16:01:17,527 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_1: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:01:20,531 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_935
2025-09-11 16:01:20,531 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:01:23,534 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Extended Service FROM DRILL DWON
2025-09-11 16:01:24,909 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:01:25,721 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:01:25,842 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:01:25,954 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:01:25,954 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 16:01:25,954 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-11 16:01:25,954 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 16:01:25,954 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_1: Data extraction successful
2025-09-11 16:01:25,954 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_1: Enhanced processing completed for 2023-11-01 from Extended Service
2025-09-11 16:01:36,826 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-11 16:01:36,860 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:01:39,884 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:01:41,298 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:01:42,324 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Internal in chart chart_935
2025-09-11 16:01:44,341 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_935 interactivity ensured
2025-09-11 16:01:45,342 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_935
2025-09-11 16:01:45,342 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_2: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:01:45,466 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_2: Chart.js event click successful, checking for navigation...
2025-09-11 16:01:48,468 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_2: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:01:51,471 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_935
2025-09-11 16:01:51,471 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:01:54,474 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Internal FROM DRILL DWON
2025-09-11 16:01:56,069 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:01:56,931 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:01:57,057 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:01:57,166 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:01:57,166 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 16:01:57,166 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-11 16:01:57,166 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 16:01:57,166 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_2: Data extraction successful
2025-09-11 16:01:57,166 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_2: Enhanced processing completed for 2023-11-01 from Internal
2025-09-11 16:02:07,839 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-11 16:02:07,868 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:02:10,897 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:02:12,308 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:02:13,336 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Maintenance Plan in chart chart_935
2025-09-11 16:02:15,369 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_935 interactivity ensured
2025-09-11 16:02:16,370 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_935
2025-09-11 16:02:16,370 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_3: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:02:16,392 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_3: Trying coordinate-based clicking...
2025-09-11 16:02:27,411 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-11 16:02:27,487 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:02:30,512 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:02:31,935 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:02:32,967 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Warranty in chart chart_935
2025-09-11 16:02:34,996 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_935 interactivity ensured
2025-09-11 16:02:35,997 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_935
2025-09-11 16:02:35,997 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_4: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:02:36,122 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_4: Chart.js event click successful, checking for navigation...
2025-09-11 16:02:39,123 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_4: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:02:42,127 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_935
2025-09-11 16:02:42,127 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:02:45,127 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Warranty FROM DRILL DWON
2025-09-11 16:02:46,351 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:02:47,192 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:02:47,309 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:02:47,423 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:02:47,423 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 16:02:47,423 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-11 16:02:47,424 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 16:02:47,424 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_4: Data extraction successful
2025-09-11 16:02:47,424 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_4: Enhanced processing completed for 2023-11-01 from Warranty
2025-09-11 16:02:58,347 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-11 16:02:58,414 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:03:01,440 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:03:02,885 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:03:03,901 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Factory Service Contract in chart chart_935
2025-09-11 16:03:05,929 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_935 interactivity ensured
2025-09-11 16:03:06,930 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_935
2025-09-11 16:03:06,930 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_5: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:03:06,947 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_935_point_5: Trying coordinate-based clicking...
2025-09-11 16:03:13,619 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-11 16:03:13,795 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:03:16,822 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:03:19,264 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:03:20,297 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Mileage Under 60k in chart chart_948
2025-09-11 16:03:22,320 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_948 interactivity ensured
2025-09-11 16:03:23,321 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_948
2025-09-11 16:03:23,322 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_948_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:03:23,461 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_0: Chart.js event click successful, checking for navigation...
2025-09-11 16:03:26,463 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:03:29,466 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_948
2025-09-11 16:03:29,466 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:03:32,467 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Mileage Under 60k FROM DRILL DWON
2025-09-11 16:03:32,475 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:03:32,590 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:03:32,710 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:03:32,830 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:03:32,830 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 16:03:32,830 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-11 16:03:32,830 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 16:03:32,830 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_0: Data extraction successful
2025-09-11 16:03:32,830 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_0: Enhanced processing completed for 2023-11-01 from Mileage Under 60k
2025-09-11 16:03:43,743 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-11 16:03:43,814 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:03:46,837 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:03:48,249 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:03:49,265 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Mileage Over 60k in chart chart_948
2025-09-11 16:03:51,284 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_948 interactivity ensured
2025-09-11 16:03:52,285 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_948
2025-09-11 16:03:52,285 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_948_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:03:52,417 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_1: Chart.js event click successful, checking for navigation...
2025-09-11 16:03:55,420 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_1: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:03:58,423 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_948
2025-09-11 16:03:58,424 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:04:01,427 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Mileage Over 60k FROM DRILL DWON
2025-09-11 16:04:01,451 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:04:01,557 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:04:01,673 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:04:01,795 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:04:01,795 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 16:04:01,795 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-11 16:04:01,795 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 16:04:01,795 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_1: Data extraction successful
2025-09-11 16:04:01,795 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_1: Enhanced processing completed for 2023-11-01 from Mileage Over 60k
2025-09-11 16:04:12,739 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-11 16:04:12,807 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:04:15,847 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:04:17,254 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:04:18,278 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Total Shop in chart chart_948
2025-09-11 16:04:20,299 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_948 interactivity ensured
2025-09-11 16:04:21,299 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_948
2025-09-11 16:04:21,299 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_948_point_2: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:04:21,400 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_2: Chart.js event click successful, checking for navigation...
2025-09-11 16:04:24,403 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_2: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:04:27,407 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_948
2025-09-11 16:04:27,407 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:04:30,411 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Total Shop FROM DRILL DWON
2025-09-11 16:04:30,442 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:04:30,555 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:04:30,671 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:04:30,779 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:04:30,780 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 16:04:30,780 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-11 16:04:30,780 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 16:04:30,780 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_2: Data extraction successful
2025-09-11 16:04:30,780 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_948_point_2: Enhanced processing completed for 2023-11-01 from Total Shop
2025-09-11 16:04:37,604 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-11 16:04:37,768 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:04:40,801 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:04:43,255 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:04:44,275 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Mileage Under 60K in chart chart_923
2025-09-11 16:04:46,294 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_923 interactivity ensured
2025-09-11 16:04:47,295 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_923
2025-09-11 16:04:47,295 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_923_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:04:47,431 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_0: Chart.js event click successful, checking for navigation...
2025-09-11 16:04:50,432 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:04:53,435 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_923
2025-09-11 16:04:53,436 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:04:56,437 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Mileage Under 60K FROM DRILL DWON
2025-09-11 16:04:56,473 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:04:56,584 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:04:56,682 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:04:56,773 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:04:56,773 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 16:04:56,773 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 6
2025-09-11 16:04:56,773 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 16:04:56,773 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_0: Data extraction successful
2025-09-11 16:04:56,773 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_0: Enhanced processing completed for 2023-11-01 from Mileage Under 60K
2025-09-11 16:05:07,379 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-11 16:05:07,445 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:05:10,475 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:05:11,887 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:05:12,921 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Mileage Over 60K in chart chart_923
2025-09-11 16:05:14,955 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_923 interactivity ensured
2025-09-11 16:05:15,957 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_923
2025-09-11 16:05:15,957 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_923_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:05:16,105 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_1: Chart.js event click successful, checking for navigation...
2025-09-11 16:05:19,108 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_1: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:05:22,111 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_923
2025-09-11 16:05:22,112 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:05:25,115 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Mileage Over 60K FROM DRILL DWON
2025-09-11 16:05:25,142 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:05:25,216 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:05:25,313 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:05:25,401 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:05:25,401 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 16:05:25,401 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 6
2025-09-11 16:05:25,401 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 16:05:25,401 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_1: Data extraction successful
2025-09-11 16:05:25,401 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_1: Enhanced processing completed for 2023-11-01 from Mileage Over 60K
2025-09-11 16:05:36,423 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-11 16:05:36,501 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:05:39,529 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:05:40,935 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:05:41,951 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Total Shop in chart chart_923
2025-09-11 16:05:43,965 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_923 interactivity ensured
2025-09-11 16:05:44,966 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_923
2025-09-11 16:05:44,966 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_923_point_2: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:05:45,073 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_2: Chart.js event click successful, checking for navigation...
2025-09-11 16:05:48,075 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_2: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:05:51,079 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_923
2025-09-11 16:05:51,079 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:05:54,082 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Total Shop FROM DRILL DWON
2025-09-11 16:05:54,090 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:05:54,162 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:05:54,258 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:05:54,346 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:05:54,346 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 16:05:54,346 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 6
2025-09-11 16:05:54,346 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 16:05:54,346 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_2: Data extraction successful
2025-09-11 16:05:54,346 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_923_point_2: Enhanced processing completed for 2023-11-01 from Total Shop
2025-09-11 16:06:01,157 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-11 16:06:01,334 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:06:04,346 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:06:06,776 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:06:07,814 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Mileage Under 60K in chart chart_1354
2025-09-11 16:06:09,846 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1354 interactivity ensured
2025-09-11 16:06:10,847 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1354
2025-09-11 16:06:10,847 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1354_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:06:10,983 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_0: Chart.js event click successful, checking for navigation...
2025-09-11 16:06:13,987 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1354
2025-09-11 16:06:16,987 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1354
2025-09-11 16:06:16,987 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:06:19,991 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Mileage Under 60K FROM DRILL DWON
2025-09-11 16:06:20,024 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:06:20,084 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:06:20,157 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:06:20,221 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:06:20,221 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 16:06:20,221 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-11 16:06:20,221 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 16:06:20,221 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_0: Data extraction successful
2025-09-11 16:06:20,221 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_0: Enhanced processing completed for 2023-11-01 from Mileage Under 60K
2025-09-11 16:06:31,135 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-11 16:06:31,162 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:06:34,171 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:06:35,585 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:06:36,613 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Mileage Over 60K in chart chart_1354
2025-09-11 16:06:38,628 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1354 interactivity ensured
2025-09-11 16:06:39,630 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1354
2025-09-11 16:06:39,630 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1354_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:06:39,781 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_1: Chart.js event click successful, checking for navigation...
2025-09-11 16:06:42,783 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_1: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1354
2025-09-11 16:06:45,786 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1354
2025-09-11 16:06:45,787 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:06:48,789 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Mileage Over 60K FROM DRILL DWON
2025-09-11 16:06:48,817 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:06:48,865 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:06:48,937 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:06:49,001 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:06:49,002 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 16:06:49,002 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-11 16:06:49,002 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 16:06:49,002 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_1: Data extraction successful
2025-09-11 16:06:49,002 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_1: Enhanced processing completed for 2023-11-01 from Mileage Over 60K
2025-09-11 16:06:59,987 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-11 16:07:00,055 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:07:03,067 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:07:04,480 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:07:05,505 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Total Shop in chart chart_1354
2025-09-11 16:07:07,534 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1354 interactivity ensured
2025-09-11 16:07:08,534 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1354
2025-09-11 16:07:08,534 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1354_point_2: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:07:08,634 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_2: Chart.js event click successful, checking for navigation...
2025-09-11 16:07:11,635 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_2: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1354
2025-09-11 16:07:14,637 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1354
2025-09-11 16:07:14,637 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:07:17,639 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Total Shop FROM DRILL DWON
2025-09-11 16:07:17,651 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:07:17,707 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:07:17,777 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:07:17,845 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:07:17,845 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 16:07:17,845 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-11 16:07:17,845 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 16:07:17,845 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_2: Data extraction successful
2025-09-11 16:07:17,845 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1354_point_2: Enhanced processing completed for 2023-11-01 from Total Shop
2025-09-11 16:07:24,999 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-11 16:07:25,381 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:07:28,396 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:07:30,847 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:07:31,876 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Mileage Under 60K in chart chart_1355
2025-09-11 16:07:33,897 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1355 interactivity ensured
2025-09-11 16:07:34,898 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1355
2025-09-11 16:07:34,898 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1355_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:07:35,043 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_0: Chart.js event click successful, checking for navigation...
2025-09-11 16:07:38,046 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:07:41,047 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1355
2025-09-11 16:07:41,048 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:07:44,051 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Mileage Under 60K FROM DRILL DWON
2025-09-11 16:07:44,082 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:07:44,168 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:07:44,262 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:07:44,349 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:07:44,349 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 16:07:44,349 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 6
2025-09-11 16:07:44,349 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 16:07:44,349 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_0: Data extraction successful
2025-09-11 16:07:44,349 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_0: Enhanced processing completed for 2023-11-01 from Mileage Under 60K
2025-09-11 16:07:55,215 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-11 16:07:55,278 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:07:58,310 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:07:59,720 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:08:00,750 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Mileage Over 60K in chart chart_1355
2025-09-11 16:08:02,768 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1355 interactivity ensured
2025-09-11 16:08:03,769 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1355
2025-09-11 16:08:03,769 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1355_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:08:03,900 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_1: Chart.js event click successful, checking for navigation...
2025-09-11 16:08:06,902 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_1: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:08:09,903 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1355
2025-09-11 16:08:09,903 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:08:12,907 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Mileage Over 60K FROM DRILL DWON
2025-09-11 16:08:12,940 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:08:13,018 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:08:13,115 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:08:13,207 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:08:13,207 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 16:08:13,207 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 6
2025-09-11 16:08:13,207 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 16:08:13,207 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_1: Data extraction successful
2025-09-11 16:08:13,207 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_1: Enhanced processing completed for 2023-11-01 from Mileage Over 60K
2025-09-11 16:08:24,175 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-11 16:08:24,238 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:08:27,267 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:08:28,699 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:08:29,716 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Total Shop in chart chart_1355
2025-09-11 16:08:31,732 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1355 interactivity ensured
2025-09-11 16:08:32,733 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1355
2025-09-11 16:08:32,733 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1355_point_2: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:08:32,846 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_2: Chart.js event click successful, checking for navigation...
2025-09-11 16:08:35,847 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_2: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:08:38,850 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1355
2025-09-11 16:08:38,851 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:08:41,851 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Total Shop FROM DRILL DWON
2025-09-11 16:08:41,882 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:08:41,970 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:08:42,066 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:08:42,177 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:08:42,177 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 16:08:42,177 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 6
2025-09-11 16:08:42,177 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 16:08:42,177 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_2: Data extraction successful
2025-09-11 16:08:42,177 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1355_point_2: Enhanced processing completed for 2023-11-01 from Total Shop
2025-09-11 16:08:48,955 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-11 16:08:49,114 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:08:52,126 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:08:54,567 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:08:55,589 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Competitive in chart chart_936
2025-09-11 16:08:57,614 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_936 interactivity ensured
2025-09-11 16:08:58,615 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_936
2025-09-11 16:08:58,615 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_936_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:08:58,793 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_0: Chart.js event click successful, checking for navigation...
2025-09-11 16:09:01,795 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:09:04,798 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_936
2025-09-11 16:09:04,799 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:09:07,799 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Competitive FROM DRILL DWON
2025-09-11 16:09:07,826 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:09:07,939 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:09:08,077 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:09:08,206 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:09:08,206 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 16:09:08,206 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-11 16:09:08,206 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 16:09:08,206 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_0: Data extraction successful
2025-09-11 16:09:08,206 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_0: Enhanced processing completed for 2023-11-01 from Competitive
2025-09-11 16:09:19,155 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-11 16:09:19,205 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:09:22,244 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:09:23,658 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:09:24,682 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Maintenance in chart chart_936
2025-09-11 16:09:26,703 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_936 interactivity ensured
2025-09-11 16:09:27,704 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_936
2025-09-11 16:09:27,704 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_936_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:09:27,850 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_1: Chart.js event click successful, checking for navigation...
2025-09-11 16:09:30,851 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_1: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:09:33,854 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_936
2025-09-11 16:09:33,855 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:09:36,855 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Maintenance FROM DRILL DWON
2025-09-11 16:09:36,884 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:09:37,029 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:09:37,152 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:09:37,269 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:09:37,269 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 16:09:37,269 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-11 16:09:37,269 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 16:09:37,269 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_1: Data extraction successful
2025-09-11 16:09:37,269 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_1: Enhanced processing completed for 2023-11-01 from Maintenance
2025-09-11 16:09:48,239 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-11 16:09:48,294 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:09:51,318 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:09:52,723 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:09:53,753 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Repair in chart chart_936
2025-09-11 16:09:55,772 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_936 interactivity ensured
2025-09-11 16:09:56,773 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_936
2025-09-11 16:09:56,773 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_936_point_2: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:09:56,937 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_2: Chart.js event click successful, checking for navigation...
2025-09-11 16:09:59,939 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_2: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:10:02,943 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_936
2025-09-11 16:10:02,943 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:10:05,947 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Repair FROM DRILL DWON
2025-09-11 16:10:05,989 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:10:06,090 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:10:06,212 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:10:06,330 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:10:06,330 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 16:10:06,330 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-11 16:10:06,330 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 16:10:06,330 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_2: Data extraction successful
2025-09-11 16:10:06,330 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_936_point_2: Enhanced processing completed for 2023-11-01 from Repair
2025-09-11 16:11:03,699 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-11 16:11:06,044 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:11:09,055 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:11:11,494 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:11:12,523 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for 12 Months Return Rate in chart chart_938
2025-09-11 16:11:14,546 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_938 interactivity ensured
2025-09-11 16:11:15,547 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_938
2025-09-11 16:11:15,547 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_938_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:11:15,725 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_938_point_0: Chart.js event click successful, checking for navigation...
2025-09-11 16:11:18,727 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_938_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:11:21,731 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_938
2025-09-11 16:11:21,731 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:11:24,735 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: 12 Months Return Rate FROM DRILL DWON
2025-09-11 16:11:24,753 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 0 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:11:24,761 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:11:24,802 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:11:24,829 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:11:24,830 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: False
2025-09-11 16:11:24,830 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 0
2025-09-11 16:11:26,831 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 2/3)
2025-09-11 16:11:29,835 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: 12 Months Return Rate FROM DRILL DWON
2025-09-11 16:11:29,858 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 0 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:11:29,871 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:11:29,908 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:11:29,938 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:11:29,938 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: False
2025-09-11 16:11:29,939 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 0
2025-09-11 16:11:31,939 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 3/3)
2025-09-11 16:11:34,942 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: 12 Months Return Rate FROM DRILL DWON
2025-09-11 16:11:34,949 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 0 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:11:34,956 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:11:34,991 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:11:35,020 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:11:35,020 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: False
2025-09-11 16:11:35,020 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 0
2025-09-11 16:11:35,020 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_938_point_0: Enhanced processing completed for 2023-11 from 12 Months Return Rate
2025-09-11 16:11:45,971 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-11 16:11:46,022 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:11:49,051 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:11:50,449 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:11:51,472 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for 6 Months Return Rate in chart chart_938
2025-09-11 16:11:53,504 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_938 interactivity ensured
2025-09-11 16:11:54,505 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_938
2025-09-11 16:11:54,505 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_938_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:11:54,627 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_938_point_1: Chart.js event click successful, checking for navigation...
2025-09-11 16:11:57,627 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_938_point_1: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:12:00,629 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_938
2025-09-11 16:12:00,630 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:12:03,632 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: 6 Months Return Rate FROM DRILL DWON
2025-09-11 16:12:03,657 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 0 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:12:03,677 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:12:03,713 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:12:03,742 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:12:03,742 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: False
2025-09-11 16:12:03,742 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 0
2025-09-11 16:12:05,743 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 2/3)
2025-09-11 16:12:08,745 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: 6 Months Return Rate FROM DRILL DWON
2025-09-11 16:12:08,768 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 0 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:12:08,777 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:12:08,816 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:12:08,845 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:12:08,845 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: False
2025-09-11 16:12:08,845 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 0
2025-09-11 16:12:10,847 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 3/3)
2025-09-11 16:12:13,851 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: 6 Months Return Rate FROM DRILL DWON
2025-09-11 16:12:13,874 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 0 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:12:13,898 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:12:13,937 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:12:13,979 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:12:13,979 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: False
2025-09-11 16:12:13,979 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 0
2025-09-11 16:12:13,979 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_938_point_1: Enhanced processing completed for 2023-11 from 6 Months Return Rate
2025-09-11 16:12:20,679 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-11 16:12:20,841 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:12:23,873 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:12:26,331 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:12:27,358 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Parts to Labor Ratio in chart chart_930
2025-09-11 16:12:29,375 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_930 interactivity ensured
2025-09-11 16:12:30,377 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_930
2025-09-11 16:12:30,377 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_930_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:12:30,511 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_930_point_0: Chart.js event click successful, checking for navigation...
2025-09-11 16:12:33,511 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_930_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:12:36,514 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_930
2025-09-11 16:12:36,515 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:12:39,515 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Parts to Labor Ratio FROM DRILL DWON
2025-09-11 16:12:39,550 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:12:39,656 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:12:39,774 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:12:39,890 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:12:39,890 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 16:12:39,890 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-11 16:12:39,890 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 16:12:39,890 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_930_point_0: Data extraction successful
2025-09-11 16:12:39,891 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_930_point_0: Enhanced processing completed for 2023-11-01 from Parts to Labor Ratio
2025-09-11 16:12:46,541 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-11 16:12:46,710 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:12:49,739 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:12:52,129 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:12:53,179 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for MPI Penetration Percentage in chart chart_1316
2025-09-11 16:12:55,193 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1316 interactivity ensured
2025-09-11 16:12:56,195 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1316
2025-09-11 16:12:56,195 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1316_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:12:56,195 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1316_point_0: Waiting for chart to load...IFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-09-11 16:13:26,353 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1316_point_0: Chart.js event click successful, checking for navigation...
2025-09-11 16:13:29,355 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1316_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1316
2025-09-11 16:13:32,357 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1316
2025-09-11 16:13:32,358 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:13:35,359 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: MPI Penetration Percentage FROM DRILL DWON
2025-09-11 16:13:35,383 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:13:35,492 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:13:35,622 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:13:35,741 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:13:35,741 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 16:13:35,741 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-11 16:13:35,741 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 16:13:35,741 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1316_point_0: Data extraction successful
2025-09-11 16:13:35,741 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1316_point_0: Enhanced processing completed for 2023-11 from MPI Penetration Percentage
2025-09-11 16:13:56,843 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-11 16:13:56,955 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 16:13:59,992 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-11 16:14:02,428 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-11 16:14:03,469 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Menu Penetration Percentage in chart chart_1317
2025-09-11 16:14:05,494 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1317 interactivity ensured
2025-09-11 16:14:06,495 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1317
2025-09-11 16:14:06,495 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1317_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 16:14:06,639 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1317_point_0: Chart.js event click successful, checking for navigation...
2025-09-11 16:14:09,643 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1317_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1317
2025-09-11 16:14:12,645 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1317
2025-09-11 16:14:12,645 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-11 16:14:15,648 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Menu Penetration Percentage FROM DRILL DWON
2025-09-11 16:14:15,920 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-11 16:14:16,381 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-11 16:14:16,500 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-11 16:14:16,619 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-11 16:14:16,620 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-11 16:14:16,620 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 9
2025-09-11 16:14:16,620 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-11 16:14:16,620 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1317_point_0: Data extraction successful
2025-09-11 16:14:16,620 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1317_point_0: Enhanced processing completed for 2023-11 from Menu Penetration Percentage
2025-09-11 16:14:16,701 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3328) All results saved to chart_processing_results/chart_processing_all.json
2025-09-11 16:14:16,701 [INFO] [FOPC QA AUTOMATION] (events.py:88) 
================================================================================
2025-09-11 16:14:16,701 [INFO] [FOPC QA AUTOMATION] (events.py:88) Parallel processing with 3 browsers completed successfully!
2025-09-11 16:14:16,701 [INFO] [FOPC QA AUTOMATION] (events.py:88) Final Results:
2025-09-11 16:14:16,701 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Total tasks processed: 33
2025-09-11 16:14:16,701 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Charts processed: 12
2025-09-11 16:14:16,701 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Batches processed: 1
2025-09-11 16:14:16,702 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Successful tasks: 26
2025-09-11 16:14:16,702 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Failed tasks: 7
2025-09-11 16:14:16,702 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Success rate: 78.8%
2025-09-11 16:14:16,702 [INFO] [FOPC QA AUTOMATION] (events.py:88) ================================================================================
2025-09-11 16:14:16,702 [INFO] [FOPC QA AUTOMATION] (events.py:88)  Parallel processing completed with 26 successful extractions
2025-09-11 16:14:16,702 [INFO] [FOPC QA AUTOMATION] (events.py:88) 
================================================================================
2025-09-11 16:14:16,702 [INFO] [FOPC QA AUTOMATION] (events.py:88) GENERATING FINAL UI vs DB COMPARISON REPORT
2025-09-11 16:14:16,702 [INFO] [FOPC QA AUTOMATION] (events.py:88) ================================================================================
2025-09-11 16:14:16,702 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:342) Target month range: 2023-11-01 to 2023-11-30
2025-09-11 16:14:16,702 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:342) Fetching data from database...
2025-09-11 16:15:07,156 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:284) Target month data shape: (2682, 48)
2025-09-11 16:15:09,438 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473) 
================================================================================
2025-09-11 16:15:09,438 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473) RESULTS PROCESSING
2025-09-11 16:15:09,438 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473) ================================================================================
2025-09-11 16:15:09,438 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473) 
Target month Special Metrics data written successfully to chart_processing_results/db_calculated_value.json
2025-09-11 16:15:09,438 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473) 
Target Month Summary for November 2023:
2025-09-11 16:15:09,438 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473)   Total Revenue: $209,355.59
2025-09-11 16:15:09,438 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473)   Total Gross Profit: $106,275.19
2025-09-11 16:15:09,438 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473)   GP Percentage: 50.8%
2025-09-11 16:15:09,438 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473)   Total ROs: 987
2025-09-11 16:15:09,438 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473)     - Customer Pay ROs: 587
2025-09-11 16:15:09,438 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473)     - Warranty ROs: 274
2025-09-11 16:15:09,438 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473)     - Internal ROs: 126
2025-09-11 16:15:09,438 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473)   Labor sold hours: 644.2
2025-09-11 16:15:09,439 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473) 
================================================================================
2025-09-11 16:15:09,439 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473) Special Metrics ANALYSIS - MAIN EXECUTION COMPLETED
2025-09-11 16:15:09,439 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3473) ================================================================================
2025-09-11 16:15:09,439 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3477) Starting CP Overview comparison...
2025-09-11 16:15:09,439 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3477) Loading UI data from: chart_processing_results/chart_processing_all.json
2025-09-11 16:15:09,439 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3477) Loading DB data from: chart_processing_results/db_calculated_value.json
2025-09-11 16:15:09,440 [INFO] [FOPC QA AUTOMATION] (compare_cp_overview.py:463) Processing 33 UI charts
2025-09-11 16:15:09,440 [INFO] [FOPC QA AUTOMATION] (compare_cp_overview.py:463) Extracted 18 UI chart values
2025-09-11 16:15:09,440 [INFO] [FOPC QA AUTOMATION] (compare_cp_overview.py:464) Target month: 2023-11-01, Processing DB values for date: 2023-11-01
2025-09-11 16:15:09,440 [INFO] [FOPC QA AUTOMATION] (compare_cp_overview.py:464) Extracted 12 DB values from CP overview data
2025-09-11 16:15:09,441 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3477) CSV comparison results saved to Individual_Reports-ginnmotorcompany/cp_overview_comparison_results.csv
2025-09-11 16:15:09,461 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3477) Excel file with highlighted mismatches saved as Individual_Reports-ginnmotorcompany/cp_overview_comparison_highlighted.xlsx
2025-09-11 16:15:09,462 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3477) JSON report saved to Individual_Reports-ginnmotorcompany/cp_overview_comparison.json
2025-09-11 16:15:09,462 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3477) HTML report saved to Individual_Reports-ginnmotorcompany/cp_overview_comparison.html
2025-09-11 16:15:09,462 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3477) CP Overview comparison completed successfully
2025-09-11 16:15:09,462 [INFO] [FOPC QA AUTOMATION] (events.py:88) End Time: 1077.5603189468384
2025-09-11 16:15:09,463 [INFO] [root] (run_all_tests.py:42) Completed: validate_metrics | Time taken: 1077.56 seconds
2025-09-11 16:15:09,463 [INFO] [root] (run_all_tests.py:202) All validations completed in 1085.23 seconds
2025-09-11 16:15:09,624 [INFO] [root] (report_generator.py:96) Combined HTML report created at: Final_Consolidated_Report-ginnmotorcompany/consolidated_report.html
