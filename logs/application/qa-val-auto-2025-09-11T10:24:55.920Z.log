2025-09-11 15:54:55,922 [INFO] [root] (run_all_tests.py:39) Started running: validate_metrics
2025-09-11 15:54:55,923 [INFO] [FOPC QA AUTOMATION] (events.py:88) Start Time: 2025-09-11 15:54:55
2025-09-11 15:54:55,923 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Processing mode: Parallel (3 browsers, different charts)
2025-09-11 15:54:55,923 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Max concurrent browsers: 3
2025-09-11 15:54:55,923 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Target months/years: ['2023-11-01']
2025-09-11 15:54:55,923 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Browser timeout: 30000ms
2025-09-11 15:54:55,923 [INFO] [FOPC QA AUTOMATION] (events.py:88) ================================================================================
2025-09-11 15:54:55,924 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3437) Auth state loaded from file
2025-09-11 15:54:55,924 [INFO] [FOPC QA AUTOMATION] (events.py:88)  Valid authentication found, proceeding with processing...
2025-09-11 15:54:55,924 [INFO] [FOPC QA AUTOMATION] (events.py:88) 
 Starting parallel chart processing workflow ...
2025-09-11 15:54:55,925 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Creating chart-point combinations...
2025-09-11 15:55:38,233 [INFO] [root] (run_all_tests.py:42) Completed: validate_metrics | Time taken: 42.31 seconds
2025-09-11 15:55:38,233 [INFO] [root] (run_all_tests.py:202) All validations completed in 50.45 seconds
2025-09-11 15:55:38,234 [INFO] [root] (report_generator.py:96) Combined HTML report created at: Final_Consolidated_Report-ginnmotorcompany/consolidated_report.html
