2025-09-12 15:44:04,037 [INFO] [root] (run_all_tests.py:39) Started running: validate_metrics
2025-09-12 15:44:04,037 [INFO] [FOPC QA AUTOMATION] (events.py:88) Start Time: 2025-09-12 15:44:04
2025-09-12 15:44:04,037 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Processing mode: Parallel (3 browsers, different charts)
2025-09-12 15:44:04,037 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Max concurrent browsers: 3
2025-09-12 15:44:04,037 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Target months/years: ['2023-11-01']
2025-09-12 15:44:04,037 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Browser timeout: 30000ms
2025-09-12 15:44:04,038 [INFO] [FOPC QA AUTOMATION] (events.py:88) ================================================================================
2025-09-12 15:44:04,038 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3437) Auth state loaded from file
2025-09-12 15:44:04,038 [INFO] [FOPC QA AUTOMATION] (events.py:88)  Valid authentication found, proceeding with processing...
2025-09-12 15:44:04,038 [INFO] [FOPC QA AUTOMATION] (events.py:88) 
 Starting parallel chart processing workflow ...
2025-09-12 15:44:04,039 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Creating chart-point combinations...
2025-09-12 15:44:28,207 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 948: CP 1-Line-RO Count
2025-09-12 15:44:28,207 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:44:28,207 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 0 for target: 2023-11-01
2025-09-12 15:44:28,217 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 0 for 2023-11-01
2025-09-12 15:44:28,217 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 0, 'datasetIndex': 0, 'pointIndex': 10, 'value': '188', 'xLabel': '2023-11-01', 'screenX': 907.0349008413461, 'screenY': 463.7152324084374, 'canvasX': 632.0349008413461, 'canvasY': 161.7152324084374, 'datasetLabel': 'Mileage Under 60k', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 0, 'datasetIndex': 1, 'pointIndex': 10, 'value': '215', 'xLabel': '2023-11-01', 'screenX': 907.0349008413461, 'screenY': 450.3437219115534, 'canvasX': 632.0349008413461, 'canvasY': 148.34372191155342, 'datasetLabel': 'Mileage Over 60k', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 0, 'datasetIndex': 2, 'pointIndex': 10, 'value': '403', 'xLabel': '2023-11-01', 'screenX': 907.0349008413461, 'screenY': 357.23838956287955, 'canvasX': 632.0349008413461, 'canvasY': 55.238389562879576, 'datasetLabel': 'Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:44:28,217 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 15:44:28,217 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 0 - 2023-11-01 (3 points)
2025-09-12 15:44:28,217 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1357: Average RO Open Days
2025-09-12 15:44:28,217 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:44:28,217 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 1 for target: 2023-11-01
2025-09-12 15:44:28,224 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 6 matching points in chart 1 for 2023-11-01
2025-09-12 15:44:28,224 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 1, 'datasetIndex': 0, 'pointIndex': 10, 'value': '3.61', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 521.0641552061844, 'canvasX': 630.7514723557692, 'canvasY': 219.06415520618438, 'datasetLabel': 'Customer Pay', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 1, 'pointIndex': 10, 'value': '1.63', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 540.6757039349476, 'canvasX': 630.7514723557692, 'canvasY': 238.6757039349476, 'datasetLabel': 'Extended Service', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 2, 'pointIndex': 10, 'value': '5.40', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 503.3345227695752, 'canvasX': 630.7514723557692, 'canvasY': 201.33452276957522, 'datasetLabel': 'Internal', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 3, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 630.7514723557692, 'canvasY': nan, 'datasetLabel': 'Maintenance', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 4, 'pointIndex': 10, 'value': '6.93', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 488.18014420644, 'canvasX': 630.7514723557692, 'canvasY': 186.18014420644, 'datasetLabel': 'Warranty', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 5, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 630.7514723557692, 'canvasY': nan, 'datasetLabel': 'Factory Service Contract', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:44:28,224 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 6 matching points for 2023-11-01
2025-09-12 15:44:28,225 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 1 - 2023-11-01 (6 points)
2025-09-12 15:44:28,225 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 923: CP 1-Line-RO Count Percentage
2025-09-12 15:44:28,225 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:44:28,225 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 2 for target: 2023-11-01
2025-09-12 15:44:28,231 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 2 for 2023-11-01
2025-09-12 15:44:28,232 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 2, 'datasetIndex': 0, 'pointIndex': 10, 'value': '70.15', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 739.6882820357193, 'canvasX': 633.44453125, 'canvasY': 37.688282035719304, 'datasetLabel': 'Mileage Under 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 2, 'datasetIndex': 1, 'pointIndex': 10, 'value': '67.40', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 748.200238949245, 'canvasX': 633.44453125, 'canvasY': 46.20023894924502, 'datasetLabel': 'Mileage Over 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 2, 'datasetIndex': 2, 'pointIndex': 10, 'value': '68.65', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 744.3311676249151, 'canvasX': 633.44453125, 'canvasY': 42.33116762491515, 'datasetLabel': 'Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:44:28,232 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 15:44:28,232 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 2 - 2023-11-01 (3 points)
2025-09-12 15:44:28,232 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1354: Multi-Line-RO Count
2025-09-12 15:44:28,232 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:44:28,232 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 3 for target: 2023-11-01
2025-09-12 15:44:28,238 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 3 for 2023-11-01
2025-09-12 15:44:28,238 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 3, 'datasetIndex': 0, 'pointIndex': 10, 'value': '80', 'xLabel': '2023-11-01', 'screenX': 1716.0349008413461, 'screenY': 877.5819840348356, 'canvasX': 632.0349008413461, 'canvasY': 175.5819840348356, 'datasetLabel': 'Mileage Under 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 3, 'datasetIndex': 1, 'pointIndex': 10, 'value': '104', 'xLabel': '2023-11-01', 'screenX': 1716.0349008413461, 'screenY': 853.810409818153, 'canvasX': 632.0349008413461, 'canvasY': 151.81040981815298, 'datasetLabel': 'Mileage Over 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 3, 'datasetIndex': 2, 'pointIndex': 10, 'value': '184', 'xLabel': '2023-11-01', 'screenX': 1716.0349008413461, 'screenY': 774.5718290958773, 'canvasX': 632.0349008413461, 'canvasY': 72.57182909587738, 'datasetLabel': 'Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:44:28,238 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 15:44:28,238 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 3 - 2023-11-01 (3 points)
2025-09-12 15:44:28,238 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1355: Multi-Line-RO Count Percentage
2025-09-12 15:44:28,238 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:44:28,238 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 4 for target: 2023-11-01
2025-09-12 15:44:28,244 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 4 for 2023-11-01
2025-09-12 15:44:28,244 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 4, 'datasetIndex': 0, 'pointIndex': 10, 'value': '29.85', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 1172.033718307117, 'canvasX': 633.44453125, 'canvasY': 70.03371830711696, 'datasetLabel': 'Mileage Under 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 4, 'datasetIndex': 1, 'pointIndex': 10, 'value': '32.60', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 1155.0098044800657, 'canvasX': 633.44453125, 'canvasY': 53.00980448006557, 'datasetLabel': 'Mileage Over 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 4, 'datasetIndex': 2, 'pointIndex': 10, 'value': '31.35', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 1162.7479471287254, 'canvasX': 633.44453125, 'canvasY': 60.7479471287253, 'datasetLabel': 'Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:44:28,244 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 15:44:28,244 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 4 - 2023-11-01 (3 points)
2025-09-12 15:44:28,244 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 938: CP Return Rate
2025-09-12 15:44:28,244 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:44:28,244 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 5 for target: 2023-11-01
2025-09-12 15:44:28,251 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 2 matching points in chart 5 for 2023-11-01
2025-09-12 15:44:28,252 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 5, 'datasetIndex': 0, 'pointIndex': 10, 'value': '8.73', 'xLabel': '2023-11', 'screenX': 1714.7514723557692, 'screenY': 1221.7125941115123, 'canvasX': 630.7514723557692, 'canvasY': 119.71259411151242, 'datasetLabel': '12 Months Return Rate', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 5, 'datasetIndex': 1, 'pointIndex': 10, 'value': '10.52', 'xLabel': '2023-11', 'screenX': 1714.7514723557692, 'screenY': 1194.0100434293106, 'canvasX': 630.7514723557692, 'canvasY': 92.01004342931061, 'datasetLabel': '6 Months Return Rate', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:44:28,252 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 2 matching points for 2023-11-01
2025-09-12 15:44:28,252 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 5 - 2023-11-01 (2 points)
2025-09-12 15:44:28,252 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 930: CP Parts to Labor Ratio
2025-09-12 15:44:28,252 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:44:28,252 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 6 for target: 2023-11-01
2025-09-12 15:44:28,257 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 1 matching points in chart 6 for 2023-11-01
2025-09-12 15:44:28,257 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 6, 'datasetIndex': 0, 'pointIndex': 10, 'value': '1.50', 'xLabel': '2023-11-01', 'screenX': 908.3172025240385, 'screenY': 1550.4700941261851, 'canvasX': 633.3172025240385, 'canvasY': 48.47009412618523, 'datasetLabel': 'Parts to Labor Ratio', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:44:28,257 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 1 matching points for 2023-11-01
2025-09-12 15:44:28,257 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 6 - 2023-11-01 (1 points)
2025-09-12 15:44:28,257 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 935: Labor Sold Hours Percentage By Pay Type
2025-09-12 15:44:28,257 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:44:28,257 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 7 for target: 2023-11-01
2025-09-12 15:44:28,264 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 6 matching points in chart 7 for 2023-11-01
2025-09-12 15:44:28,264 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 7, 'datasetIndex': 0, 'pointIndex': 10, 'value': '0.57', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1639.2007964974835, 'canvasX': 634.727959735577, 'canvasY': 137.2007964974834, 'datasetLabel': 'Customer Pay', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 1, 'pointIndex': 10, 'value': '0.05', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1746.5030412255649, 'canvasX': 634.727959735577, 'canvasY': 244.50304122556494, 'datasetLabel': 'Extended Service', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 2, 'pointIndex': 10, 'value': '0.18', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1719.6774800435446, 'canvasX': 634.727959735577, 'canvasY': 217.67748004354456, 'datasetLabel': 'Internal', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 3, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 634.727959735577, 'canvasY': nan, 'datasetLabel': 'Maintenance Plan', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 4, 'pointIndex': 10, 'value': '0.19', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1717.6139753372354, 'canvasX': 634.727959735577, 'canvasY': 215.6139753372353, 'datasetLabel': 'Warranty', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 5, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 634.727959735577, 'canvasY': nan, 'datasetLabel': 'Factory Service Contract', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:44:28,264 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 6 matching points for 2023-11-01
2025-09-12 15:44:28,264 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 7 - 2023-11-01 (6 points)
2025-09-12 15:44:28,264 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 936: CP Parts to Labor Ratio By Category
2025-09-12 15:44:28,264 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:44:28,265 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 8 for target: 2023-11-01
2025-09-12 15:44:28,271 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 8 for 2023-11-01
2025-09-12 15:44:28,271 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 8, 'datasetIndex': 0, 'pointIndex': 10, 'value': '3.37', 'xLabel': '2023-11-01', 'screenX': 906.3926231971154, 'screenY': 1948.200238949245, 'canvasX': 631.3926231971154, 'canvasY': 46.20023894924502, 'datasetLabel': 'Competitive', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 8, 'datasetIndex': 1, 'pointIndex': 10, 'value': '1.72', 'xLabel': '2023-11-01', 'screenX': 906.3926231971154, 'screenY': 2050.3437219115535, 'canvasX': 631.3926231971154, 'canvasY': 148.34372191155342, 'datasetLabel': 'Maintenance', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 8, 'datasetIndex': 2, 'pointIndex': 10, 'value': '0.95', 'xLabel': '2023-11-01', 'screenX': 906.3926231971154, 'screenY': 2098.0106806272975, 'canvasX': 631.3926231971154, 'canvasY': 196.01068062729732, 'datasetLabel': 'Repair', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:44:28,271 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 15:44:28,271 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 8 - 2023-11-01 (3 points)
2025-09-12 15:44:28,271 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1239: Revenue - Shop Supplies
2025-09-12 15:44:28,271 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:44:28,271 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 9 for target: 2023-11-01
2025-09-12 15:44:28,277 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 3 matching points in chart 9 for 2023-11-01
2025-09-12 15:44:28,277 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 9, 'datasetIndex': 0, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 633.3172025240385, 'canvasY': nan, 'datasetLabel': 'Customer Pay', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 9, 'datasetIndex': 1, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 633.3172025240385, 'canvasY': nan, 'datasetLabel': 'Warranty', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 9, 'datasetIndex': 2, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 633.3172025240385, 'canvasY': nan, 'datasetLabel': 'Internal', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:44:28,277 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 3 matching points for 2023-11-01
2025-09-12 15:44:28,277 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 9 - 2023-11-01 (3 points)
2025-09-12 15:44:28,277 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1316: MPI Penetration Percentage
2025-09-12 15:44:28,277 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:44:28,277 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 10 for target: 2023-11-01
2025-09-12 15:44:28,283 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 1 matching points in chart 10 for 2023-11-01
2025-09-12 15:44:28,283 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 10, 'datasetIndex': 0, 'pointIndex': 10, 'value': '1.25', 'xLabel': '2023-11', 'screenX': 906.3926231971154, 'screenY': 2433.0102823785555, 'canvasX': 631.3926231971154, 'canvasY': 131.01028237855562, 'datasetLabel': 'MPI Penetration Percentage', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:44:28,283 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 1 matching points for 2023-11-01
2025-09-12 15:44:28,283 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 10 - 2023-11-01 (1 points)
2025-09-12 15:44:28,283 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Processing Chart 1317: Menu Penetration Percentage
2025-09-12 15:44:28,283 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Looking for data points matching: 2023-11-01
2025-09-12 15:44:28,283 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Finding points in chart 11 for target: 2023-11-01
2025-09-12 15:44:28,288 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309)  Found 1 matching points in chart 11 for 2023-11-01
2025-09-12 15:44:28,288 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1309) Matching points: [{'canvasIndex': 11, 'datasetIndex': 0, 'pointIndex': 10, 'value': '1.53', 'xLabel': '2023-11', 'screenX': 1716.161102764423, 'screenY': 2405.2767791257593, 'canvasX': 632.161102764423, 'canvasY': 103.27677912575918, 'datasetLabel': 'Menu Penetration Percentage', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}]
2025-09-12 15:44:28,288 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) Found 1 matching points for 2023-11-01
2025-09-12 15:44:28,288 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)    Added combination: Chart 11 - 2023-11-01 (1 points)
2025-09-12 15:44:28,288 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 1: Average RO Open Days (6 points)
2025-09-12 15:44:28,288 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 7: Labor Sold Hours Percentage By Pay Type (6 points)
2025-09-12 15:44:28,288 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 0: CP 1-Line-RO Count (3 points)
2025-09-12 15:44:28,288 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 2: CP 1-Line-RO Count Percentage (3 points)
2025-09-12 15:44:28,289 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 3: Multi-Line-RO Count (3 points)
2025-09-12 15:44:28,289 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 4: Multi-Line-RO Count Percentage (3 points)
2025-09-12 15:44:28,289 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 8: CP Parts to Labor Ratio By Category (3 points)
2025-09-12 15:44:28,289 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 9: Revenue - Shop Supplies (3 points)
2025-09-12 15:44:28,289 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 5: CP Return Rate (2 points)
2025-09-12 15:44:28,289 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 6: CP Parts to Labor Ratio (1 points)
2025-09-12 15:44:28,289 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 10: MPI Penetration Percentage (1 points)
2025-09-12 15:44:28,289 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   Added Chart 11: Menu Penetration Percentage (1 points)
2025-09-12 15:44:28,289 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302) 
Summary: Created 12 chart-point combinations from 12 charts
2025-09-12 15:44:28,289 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1357: 1 combinations
2025-09-12 15:44:28,289 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   935: 1 combinations
2025-09-12 15:44:28,289 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   948: 1 combinations
2025-09-12 15:44:28,289 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   923: 1 combinations
2025-09-12 15:44:28,289 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1354: 1 combinations
2025-09-12 15:44:28,289 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1355: 1 combinations
2025-09-12 15:44:28,289 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   936: 1 combinations
2025-09-12 15:44:28,289 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1239: 1 combinations
2025-09-12 15:44:28,289 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   938: 1 combinations
2025-09-12 15:44:28,289 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   930: 1 combinations
2025-09-12 15:44:28,289 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1316: 1 combinations
2025-09-12 15:44:28,289 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3302)   1317: 1 combinations
2025-09-12 15:44:35,539 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139) Waiting for charts to load...
2025-09-12 15:44:35,731 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:44:38,744 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3139)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:44:41,146 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:44:42,165 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Customer Pay in chart chart_1357
2025-09-12 15:44:44,196 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-12 15:44:45,197 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-12 15:44:45,197 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_0: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:44:45,338 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_0: Chart.js event click successful, checking for navigation...
2025-09-12 15:44:48,341 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_0: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 15:44:51,344 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1357
2025-09-12 15:44:51,344 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:44:54,347 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Customer Pay FROM DRILL DWON
2025-09-12 15:44:54,383 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:44:54,454 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:44:54,534 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:44:54,601 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:44:54,601 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 15:44:54,601 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-12 15:44:54,602 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 15:44:54,602 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_0: Data extraction successful
2025-09-12 15:44:54,602 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_0: Enhanced processing completed for 2023-11-01 from Customer Pay
2025-09-12 15:45:05,496 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:45:05,516 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:45:08,549 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:45:09,995 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:45:11,021 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Extended Service in chart chart_1357
2025-09-12 15:45:13,040 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-12 15:45:14,041 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-12 15:45:14,041 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_1: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:45:14,159 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_1: Chart.js event click successful, checking for navigation...
2025-09-12 15:45:17,162 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_1: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 15:45:20,163 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1357
2025-09-12 15:45:20,163 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:45:23,167 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Extended Service FROM DRILL DWON
2025-09-12 15:45:23,174 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:45:23,224 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:45:23,304 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:45:23,377 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:45:23,377 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 15:45:23,377 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-12 15:45:23,377 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 15:45:23,377 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_1: Data extraction successful
2025-09-12 15:45:23,377 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_1: Enhanced processing completed for 2023-11-01 from Extended Service
2025-09-12 15:45:34,283 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:45:34,310 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:45:37,320 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:45:38,710 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3161)  All legends disabled
2025-09-12 15:45:39,737 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3166)  Enabled only legend for Internal in chart chart_1357
2025-09-12 15:45:41,776 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3176)  Chart chart_1357 interactivity ensured
2025-09-12 15:45:42,777 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------222>chart_1357
2025-09-12 15:45:42,777 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_1357_point_2: Current URL before click: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:45:42,904 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_2: Chart.js event click successful, checking for navigation...
2025-09-12 15:45:45,907 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_2: Navigation successful to: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 15:45:48,907 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190) Chart ID-----------------55-->: chart_1357
2025-09-12 15:45:48,907 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extracting drill-down page data... (Attempt 1/3)
2025-09-12 15:45:51,910 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------1-->: , Dataset Label: Internal FROM DRILL DWON
2025-09-12 15:45:51,919 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-12 15:45:51,972 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-12 15:45:52,052 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-12 15:45:52,121 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Chart ID-----------------4-->: 
2025-09-12 15:45:52,121 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Extraction success: True
2025-09-12 15:45:52,121 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Total items found: 3
2025-09-12 15:45:52,121 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Data extraction successful on attempt 1
2025-09-12 15:45:52,121 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_2: Data extraction successful
2025-09-12 15:45:52,121 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_2: Enhanced processing completed for 2023-11-01 from Internal
2025-09-12 15:46:02,879 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222) Waiting for charts to load...
2025-09-12 15:46:02,906 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:46:05,918 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3222)  Enhanced legend control applied successfully with comprehensive detection
2025-09-12 15:46:06,674 [INFO] [root] (run_all_tests.py:42) Completed: validate_metrics | Time taken: 122.64 seconds
2025-09-12 15:46:06,675 [INFO] [root] (run_all_tests.py:202) All validations completed in 130.80 seconds
2025-09-12 15:46:06,870 [INFO] [root] (report_generator.py:96) Combined HTML report created at: Final_Consolidated_Report-ginnmotorcompany/consolidated_report.html
