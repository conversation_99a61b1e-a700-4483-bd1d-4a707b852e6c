2025-09-11 15:59:37,910 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_3: Error in enhanced processing: cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-11 16:00:26,254 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_5: Error in enhanced processing: cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-11 16:02:16,392 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_3: Error in enhanced processing: cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-11 16:03:06,947 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_5: Error in enhanced processing: cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-11 16:11:24,830 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Attempt 1 failed, retrying in 2 seconds...
2025-09-11 16:11:29,939 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Attempt 2 failed, retrying in 2 seconds...
2025-09-11 16:11:35,020 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:2939) All 3 attempts failed
2025-09-11 16:11:35,020 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_938_point_0: Data extraction failed or incomplete
2025-09-11 16:12:03,742 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Attempt 1 failed, retrying in 2 seconds...
2025-09-11 16:12:08,845 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Attempt 2 failed, retrying in 2 seconds...
2025-09-11 16:12:13,979 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:2939) All 3 attempts failed
2025-09-11 16:12:13,979 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_938_point_1: Data extraction failed or incomplete
2025-09-11 16:14:16,702 [ERROR] [FOPC QA AUTOMATION] (events.py:88)  7 tasks failed - check failed results file for details
2025-09-11 16:15:09,440 [ERROR] [FOPC QA AUTOMATION] (compare_cp_overview.py:463) Error processing chart 3: float() argument must be a string or a real number, not 'NoneType'
2025-09-11 16:15:09,440 [ERROR] [FOPC QA AUTOMATION] (compare_cp_overview.py:463) Error processing chart 5: float() argument must be a string or a real number, not 'NoneType'
2025-09-11 16:15:09,440 [ERROR] [FOPC QA AUTOMATION] (compare_cp_overview.py:463) Error processing chart 9: float() argument must be a string or a real number, not 'NoneType'
2025-09-11 16:15:09,440 [ERROR] [FOPC QA AUTOMATION] (compare_cp_overview.py:463) Error processing chart 11: float() argument must be a string or a real number, not 'NoneType'
