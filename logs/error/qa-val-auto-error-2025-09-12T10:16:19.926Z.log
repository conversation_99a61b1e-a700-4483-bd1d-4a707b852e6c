2025-09-12 15:48:30,846 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_3: Error in enhanced processing: cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 15:49:19,239 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_5: <PERSON>rror in enhanced processing: cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 15:50:17,720 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Attempt 1 failed, retrying in 2 seconds...
2025-09-12 15:51:17,842 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_3: Error in enhanced processing: cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 15:52:09,694 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_5: <PERSON>rror in enhanced processing: cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 15:55:04,685 [ERROR] [FOPC QA AUTOMATION] (run_all_tests.py:40)  Processing interrupted by user
2025-09-12 15:55:04,688 [ERROR] [asyncio] (base_events.py:1833) Future exception was never retrieved
future: <Future finished exception=TargetClosedError('Target page, context or browser has been closed\nCall log:\n  - navigating to "https://ginnmotorcompany.fixedops.cc/SpecialMetrics", waiting until "load"\n')>
playwright._impl._errors.TargetClosedError: Target page, context or browser has been closed
Call log:
  - navigating to "https://ginnmotorcompany.fixedops.cc/SpecialMetrics", waiting until "load"

