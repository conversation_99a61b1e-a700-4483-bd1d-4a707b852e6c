2025-09-12 10:09:04,173 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_3: Error in enhanced processing: cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 10:09:52,571 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1357_point_5: Error in enhanced processing: cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 10:11:47,286 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_3: Error in enhanced processing: cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 10:12:38,658 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_935_point_5: Error in enhanced processing: cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 10:19:56,513 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1239_point_0: Error in enhanced processing: cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 10:20:15,695 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1239_point_1: Error in enhanced processing: cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 10:20:35,259 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3190)  chart_1239_point_2: Error in enhanced processing: cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 10:21:01,300 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Attempt 1 failed, retrying in 2 seconds...
2025-09-12 10:21:06,415 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Attempt 2 failed, retrying in 2 seconds...
2025-09-12 10:21:11,531 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:2939) All 3 attempts failed
2025-09-12 10:21:11,531 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_938_point_0: Data extraction failed or incomplete
2025-09-12 10:21:40,224 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Attempt 1 failed, retrying in 2 seconds...
2025-09-12 10:21:45,336 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:2939) Attempt 2 failed, retrying in 2 seconds...
2025-09-12 10:21:50,422 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:2939) All 3 attempts failed
2025-09-12 10:21:50,422 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3190) chart_938_point_1: Data extraction failed or incomplete
2025-09-12 10:24:04,830 [ERROR] [FOPC QA AUTOMATION] (events.py:88)  9 tasks failed - check failed results file for details
2025-09-12 10:24:42,644 [ERROR] [FOPC QA AUTOMATION] (compare_cp_overview.py:463) Error processing chart 3: float() argument must be a string or a real number, not 'NoneType'
2025-09-12 10:24:42,644 [ERROR] [FOPC QA AUTOMATION] (compare_cp_overview.py:463) Error processing chart 5: float() argument must be a string or a real number, not 'NoneType'
2025-09-12 10:24:42,644 [ERROR] [FOPC QA AUTOMATION] (compare_cp_overview.py:463) Error processing chart 9: float() argument must be a string or a real number, not 'NoneType'
2025-09-12 10:24:42,644 [ERROR] [FOPC QA AUTOMATION] (compare_cp_overview.py:463) Error processing chart 11: float() argument must be a string or a real number, not 'NoneType'
2025-09-12 10:24:42,644 [ERROR] [FOPC QA AUTOMATION] (compare_cp_overview.py:463) Error processing chart 27: float() argument must be a string or a real number, not 'NoneType'
2025-09-12 10:24:42,644 [ERROR] [FOPC QA AUTOMATION] (compare_cp_overview.py:463) Error processing chart 28: float() argument must be a string or a real number, not 'NoneType'
2025-09-12 10:24:42,644 [ERROR] [FOPC QA AUTOMATION] (compare_cp_overview.py:463) Error processing chart 29: float() argument must be a string or a real number, not 'NoneType'
