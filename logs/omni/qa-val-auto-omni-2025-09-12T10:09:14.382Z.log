2025-09-12 15:39:25,082 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🚀 Starting complete chart processing workflow with single browser sequential processing...
2025-09-12 15:39:31,697 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Discovering charts on new component page: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:39:38,349 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✓ Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:39:38,349 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ⏳ Waiting for charts to fully load...
2025-09-12 15:39:46,390 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✓ Chart 1239 container found
2025-09-12 15:39:46,395 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✓ All charts appear to be loaded
2025-09-12 15:39:46,404 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 📊 Found 12 charts on the new component page
2025-09-12 15:39:46,404 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✅ Charts with data: 12
2025-09-12 15:39:46,404 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ❌ Charts with no data: 0
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 1. Chart ID: 948 - CP 1-Line-RO Count ✅ HAS DATA
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-948
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_948 (Method: container-based)
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 302, 'width': 777, 'height': 329}
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 2. Chart ID: 1357 - Average RO Open Days ✅ HAS DATA
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1357
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1357 (Method: container-based)
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 302, 'width': 777, 'height': 329}
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 3. Chart ID: 923 - CP 1-Line-RO Count Percentage ✅ HAS DATA
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-923
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_923 (Method: container-based)
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 702, 'width': 777, 'height': 329}
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 4. Chart ID: 1354 - Multi-Line-RO Count ✅ HAS DATA
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1354
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1354 (Method: container-based)
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 702, 'width': 777, 'height': 329}
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 5. Chart ID: 1355 - Multi-Line-RO Count Percentage ✅ HAS DATA
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1355
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1355 (Method: container-based)
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 1102, 'width': 777, 'height': 329}
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 6. Chart ID: 938 - CP Return Rate ✅ HAS DATA
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-938
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_938 (Method: container-based)
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 1102, 'width': 777, 'height': 329}
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 7. Chart ID: 930 - CP Parts to Labor Ratio ✅ HAS DATA
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-930
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_930 (Method: container-based)
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 1502, 'width': 777, 'height': 329}
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 8. Chart ID: 935 - Labor Sold Hours Percentage By Pay Type ✅ HAS DATA
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-935
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_935 (Method: container-based)
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 1502, 'width': 777, 'height': 329}
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 9. Chart ID: 936 - CP Parts to Labor Ratio By Category ✅ HAS DATA
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-936
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_936 (Method: container-based)
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 1902, 'width': 777, 'height': 329}
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 10. Chart ID: 1239 - Revenue - Shop Supplies ✅ HAS DATA
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1239
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1239 (Method: container-based)
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 1902, 'width': 777, 'height': 329}
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 11. Chart ID: 1316 - MPI Penetration Percentage ✅ HAS DATA
2025-09-12 15:39:46,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1316
2025-09-12 15:39:46,406 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1316 (Method: container-based)
2025-09-12 15:39:46,406 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 2302, 'width': 777, 'height': 329}
2025-09-12 15:39:46,406 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:39:46,406 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 12. Chart ID: 1317 - Menu Penetration Percentage ✅ HAS DATA
2025-09-12 15:39:46,406 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1317
2025-09-12 15:39:46,406 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1317 (Method: container-based)
2025-09-12 15:39:46,406 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 2302, 'width': 777, 'height': 329}
2025-09-12 15:39:46,406 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:39:46,410 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) All chart container IDs on page: ['chartContainterId-948', 'chartContainterId-1357', 'chartContainterId-923', 'chartContainterId-1354', 'chartContainterId-1355', 'chartContainterId-938', 'chartContainterId-930', 'chartContainterId-935', 'chartContainterId-936', 'chartContainterId-1239', 'chartContainterId-1316', 'chartContainterId-1317']
2025-09-12 15:39:46,410 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✓ Chart 1239 found: Revenue - Shop Supplies (Index: 9)
2025-09-12 15:39:46,642 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Created 12 chart-point combinations
2025-09-12 15:39:46,642 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Step 2: Processing combinations sequentially with single browser...
2025-09-12 15:39:46,642 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) chart_data-----------------------> {'chart_index': 'chart_1', 'chart_id': '1357', 'chart_info': {'canvasIndex': 1, 'chartTitle': 'Average RO Open Days', 'canvasId': 'chart_1357', 'canvasClass': 'chartjs-render-monitor', 'containerId': 'chartContainterId-1357', 'chartId': '1357', 'position': {'x': 1084, 'y': 302, 'width': 777, 'height': 329}, 'visible': True, 'isChartJs': True, 'hasData': True, 'detectionMethod': 'container-based', 'isFullyLoaded': True}, 'target_month_year': '2023-11-01', 'matching_points': [{'canvasIndex': 1, 'datasetIndex': 0, 'pointIndex': 10, 'value': '3.61', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 521.0641552061844, 'canvasX': 630.7514723557692, 'canvasY': 219.06415520618438, 'datasetLabel': 'Customer Pay', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 1, 'pointIndex': 10, 'value': '1.63', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 540.6757039349476, 'canvasX': 630.7514723557692, 'canvasY': 238.6757039349476, 'datasetLabel': 'Extended Service', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 2, 'pointIndex': 10, 'value': '5.40', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 503.3345227695752, 'canvasX': 630.7514723557692, 'canvasY': 201.33452276957522, 'datasetLabel': 'Internal', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 3, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 630.7514723557692, 'canvasY': nan, 'datasetLabel': 'Maintenance', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 4, 'pointIndex': 10, 'value': '6.93', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 488.18014420644, 'canvasX': 630.7514723557692, 'canvasY': 186.18014420644, 'datasetLabel': 'Warranty', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 5, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 630.7514723557692, 'canvasY': nan, 'datasetLabel': 'Factory Service Contract', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}], 'processing_status': 'pending', 'points_count': 6}
2025-09-12 15:39:46,642 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: Average RO Open Days (chart_1357) with 6 points
2025-09-12 15:39:47,362 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_1357
2025-09-12 15:39:58,818 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_1357
2025-09-12 15:39:58,818 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/6: 2023-11-01 (Customer Pay)
2025-09-12 15:39:58,818 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-12 15:40:00,203 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Customer Pay
2025-09-12 15:40:02,223 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-12 15:40:02,223 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-12 15:40:03,253 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-12 15:40:12,635 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11-01
2025-09-12 15:40:12,635 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 15:40:12,635 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 15:40:12,635 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 15:40:27,627 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 15:40:27,627 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/6: 2023-11-01 (Extended Service)
2025-09-12 15:40:27,627 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-12 15:40:29,006 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Extended Service
2025-09-12 15:40:31,031 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-12 15:40:31,031 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-12 15:40:32,055 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-12 15:40:41,451 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2023-11-01
2025-09-12 15:40:41,451 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 15:40:41,451 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 15:40:41,451 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 15:40:56,574 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 15:40:56,574 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/6: 2023-11-01 (Internal)
2025-09-12 15:40:56,574 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-12 15:40:58,023 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Internal
2025-09-12 15:41:00,050 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-12 15:41:00,050 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-12 15:41:01,078 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-12 15:41:10,435 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2023-11-01
2025-09-12 15:41:10,435 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 15:41:10,435 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 15:41:10,435 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 15:41:25,258 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 15:41:25,258 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 4/6: 2023-11-01 (Maintenance)
2025-09-12 15:41:25,258 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-12 15:41:26,667 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Maintenance
2025-09-12 15:41:28,691 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-12 15:41:28,691 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-12 15:41:29,727 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-12 15:41:29,744 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 4: 2023-11-01 - cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 15:41:29,744 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 15:41:44,691 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 15:41:44,691 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 5/6: 2023-11-01 (Warranty)
2025-09-12 15:41:44,691 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-12 15:41:46,136 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Warranty
2025-09-12 15:41:48,167 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-12 15:41:48,167 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-12 15:41:49,185 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-12 15:41:58,532 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 5: 2023-11-01
2025-09-12 15:41:58,532 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 15:41:58,532 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 15:41:58,532 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 15:42:13,213 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 15:42:13,213 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 6/6: 2023-11-01 (Factory Service Contract)
2025-09-12 15:42:13,213 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-12 15:42:14,602 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Factory Service Contract
2025-09-12 15:42:16,619 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-12 15:42:16,619 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-12 15:42:17,651 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-12 15:42:17,668 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 6: 2023-11-01 - cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 15:42:17,668 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: Average RO Open Days
2025-09-12 15:42:17,771 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) chart_data-----------------------> {'chart_index': 'chart_7', 'chart_id': '935', 'chart_info': {'canvasIndex': 7, 'chartTitle': 'Labor Sold Hours Percentage By Pay Type', 'canvasId': 'chart_935', 'canvasClass': 'chartjs-render-monitor', 'containerId': 'chartContainterId-935', 'chartId': '935', 'position': {'x': 1084, 'y': 1502, 'width': 777, 'height': 329}, 'visible': True, 'isChartJs': True, 'hasData': True, 'detectionMethod': 'container-based', 'isFullyLoaded': True}, 'target_month_year': '2023-11-01', 'matching_points': [{'canvasIndex': 7, 'datasetIndex': 0, 'pointIndex': 10, 'value': '0.57', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1639.2007964974835, 'canvasX': 634.727959735577, 'canvasY': 137.2007964974834, 'datasetLabel': 'Customer Pay', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 1, 'pointIndex': 10, 'value': '0.05', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1746.5030412255649, 'canvasX': 634.727959735577, 'canvasY': 244.50304122556494, 'datasetLabel': 'Extended Service', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 2, 'pointIndex': 10, 'value': '0.18', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1719.6774800435446, 'canvasX': 634.727959735577, 'canvasY': 217.67748004354456, 'datasetLabel': 'Internal', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 3, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 634.727959735577, 'canvasY': nan, 'datasetLabel': 'Maintenance Plan', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 4, 'pointIndex': 10, 'value': '0.19', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1717.6139753372354, 'canvasX': 634.727959735577, 'canvasY': 215.6139753372353, 'datasetLabel': 'Warranty', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 5, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 634.727959735577, 'canvasY': nan, 'datasetLabel': 'Factory Service Contract', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}], 'processing_status': 'pending', 'points_count': 6}
2025-09-12 15:42:17,771 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: Labor Sold Hours Percentage By Pay Type (chart_935) with 6 points
2025-09-12 15:42:18,506 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_935
2025-09-12 15:42:34,632 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_935
2025-09-12 15:42:34,632 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/6: 2023-11-01 (Customer Pay)
2025-09-12 15:42:34,632 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-12 15:42:36,043 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Customer Pay
2025-09-12 15:42:38,063 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-12 15:42:38,063 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-12 15:42:39,086 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-12 15:42:51,523 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11-01
2025-09-12 15:42:51,523 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 15:42:51,523 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:42:51,523 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 15:43:06,233 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 15:43:06,233 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/6: 2023-11-01 (Extended Service)
2025-09-12 15:43:06,233 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-12 15:43:07,636 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Extended Service
2025-09-12 15:43:09,660 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-12 15:43:09,660 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-12 15:43:10,686 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-12 15:43:22,382 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2023-11-01
2025-09-12 15:43:22,382 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 15:43:22,382 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:43:22,382 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 15:43:37,174 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 15:43:37,174 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/6: 2023-11-01 (Internal)
2025-09-12 15:43:37,174 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-12 15:43:38,771 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Internal
2025-09-12 15:43:40,803 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-12 15:43:40,803 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-12 15:43:41,823 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
