2025-09-11 15:57:21,855 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🚀 Starting complete chart processing workflow with single browser sequential processing...
2025-09-11 15:57:28,423 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Discovering charts on new component page: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-11 15:57:35,049 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✓ Found charts using selector: canvas.chartjs-render-monitor
2025-09-11 15:57:35,049 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ⏳ Waiting for charts to fully load...
2025-09-11 15:57:43,093 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✓ Chart 1239 container found
2025-09-11 15:57:43,098 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✓ All charts appear to be loaded
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 📊 Found 12 charts on the new component page
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✅ Charts with data: 12
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ❌ Charts with no data: 0
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 1. Chart ID: 948 - CP 1-Line-RO Count ✅ HAS DATA
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-948
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_948 (Method: container-based)
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 302, 'width': 777, 'height': 329}
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 2. Chart ID: 1357 - Average RO Open Days ✅ HAS DATA
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1357
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1357 (Method: container-based)
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 302, 'width': 777, 'height': 329}
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 3. Chart ID: 923 - CP 1-Line-RO Count Percentage ✅ HAS DATA
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-923
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_923 (Method: container-based)
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 702, 'width': 777, 'height': 329}
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 4. Chart ID: 1354 - Multi-Line-RO Count ✅ HAS DATA
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1354
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1354 (Method: container-based)
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 702, 'width': 777, 'height': 329}
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 5. Chart ID: 1355 - Multi-Line-RO Count Percentage ✅ HAS DATA
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1355
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1355 (Method: container-based)
2025-09-11 15:57:43,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 1102, 'width': 777, 'height': 329}
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 6. Chart ID: 938 - CP Return Rate ✅ HAS DATA
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-938
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_938 (Method: container-based)
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 1102, 'width': 777, 'height': 329}
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 7. Chart ID: 930 - CP Parts to Labor Ratio ✅ HAS DATA
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-930
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_930 (Method: container-based)
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 1502, 'width': 777, 'height': 329}
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 8. Chart ID: 935 - Labor Sold Hours Percentage By Pay Type ✅ HAS DATA
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-935
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_935 (Method: container-based)
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 1502, 'width': 777, 'height': 329}
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 9. Chart ID: 936 - CP Parts to Labor Ratio By Category ✅ HAS DATA
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-936
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_936 (Method: container-based)
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 1902, 'width': 777, 'height': 329}
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 10. Chart ID: 1239 - Revenue - Shop Supplies ✅ HAS DATA
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1239
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1239 (Method: container-based)
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 1902, 'width': 777, 'height': 329}
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 11. Chart ID: 1316 - MPI Penetration Percentage ✅ HAS DATA
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1316
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1316 (Method: container-based)
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 2302, 'width': 777, 'height': 329}
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 12. Chart ID: 1317 - Menu Penetration Percentage ✅ HAS DATA
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1317
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1317 (Method: container-based)
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 2302, 'width': 777, 'height': 329}
2025-09-11 15:57:43,108 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-11 15:57:43,112 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) All chart container IDs on page: ['chartContainterId-948', 'chartContainterId-1357', 'chartContainterId-923', 'chartContainterId-1354', 'chartContainterId-1355', 'chartContainterId-938', 'chartContainterId-930', 'chartContainterId-935', 'chartContainterId-936', 'chartContainterId-1239', 'chartContainterId-1316', 'chartContainterId-1317']
2025-09-11 15:57:43,113 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✓ Chart 1239 found: Revenue - Shop Supplies (Index: 9)
2025-09-11 15:57:43,345 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Created 12 chart-point combinations
2025-09-11 15:57:43,345 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Step 2: Processing combinations sequentially with single browser...
2025-09-11 15:57:43,345 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: Average RO Open Days (chart_1357) with 6 points
2025-09-11 15:57:44,071 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_1357
2025-09-11 15:58:06,991 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_1357
2025-09-11 15:58:06,991 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/6: 2023-11-01 (Customer Pay)
2025-09-11 15:58:06,991 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-11 15:58:08,385 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Customer Pay
2025-09-11 15:58:10,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-11 15:58:10,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-11 15:58:11,427 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-11 15:58:20,772 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11-01
2025-09-11 15:58:20,772 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 15:58:20,772 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-11 15:58:20,772 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-11 15:58:35,771 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-11 15:58:35,771 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/6: 2023-11-01 (Extended Service)
2025-09-11 15:58:35,771 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-11 15:58:37,197 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Extended Service
2025-09-11 15:58:39,216 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-11 15:58:39,217 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-11 15:58:40,239 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-11 15:58:49,592 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2023-11-01
2025-09-11 15:58:49,592 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 15:58:49,592 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-11 15:58:49,592 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-11 15:59:04,541 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-11 15:59:04,541 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/6: 2023-11-01 (Internal)
2025-09-11 15:59:04,541 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-11 15:59:05,922 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Internal
2025-09-11 15:59:07,958 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-11 15:59:07,958 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-11 15:59:08,985 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-11 15:59:18,363 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2023-11-01
2025-09-11 15:59:18,363 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 15:59:18,363 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-11 15:59:18,363 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-11 15:59:33,460 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-11 15:59:33,460 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 4/6: 2023-11-01 (Maintenance)
2025-09-11 15:59:33,460 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-11 15:59:34,855 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Maintenance
2025-09-11 15:59:36,874 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-11 15:59:36,875 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-11 15:59:37,891 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-11 15:59:37,910 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 4: 2023-11-01 - cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-11 15:59:37,910 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-11 15:59:52,875 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-11 15:59:52,876 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 5/6: 2023-11-01 (Warranty)
2025-09-11 15:59:52,876 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-11 15:59:54,295 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Warranty
2025-09-11 15:59:56,329 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-11 15:59:56,330 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-11 15:59:57,361 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-11 16:00:06,715 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 5: 2023-11-01
2025-09-11 16:00:06,715 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 16:00:06,715 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-11 16:00:06,715 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-11 16:00:21,777 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-11 16:00:21,777 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 6/6: 2023-11-01 (Factory Service Contract)
2025-09-11 16:00:21,777 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-11 16:00:23,187 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Factory Service Contract
2025-09-11 16:00:25,207 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-11 16:00:25,207 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-11 16:00:26,235 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-11 16:00:26,254 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 6: 2023-11-01 - cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-11 16:00:26,254 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: Average RO Open Days
2025-09-11 16:00:26,345 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: Labor Sold Hours Percentage By Pay Type (chart_935) with 6 points
2025-09-11 16:00:27,043 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_935
2025-09-11 16:00:38,571 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_935
2025-09-11 16:00:38,571 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/6: 2023-11-01 (Customer Pay)
2025-09-11 16:00:38,571 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-11 16:00:39,940 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Customer Pay
2025-09-11 16:00:41,965 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-11 16:00:41,965 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-11 16:00:42,985 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-11 16:00:55,044 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11-01
2025-09-11 16:00:55,044 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 16:00:55,044 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:00:55,044 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-11 16:01:09,961 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-11 16:01:09,961 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/6: 2023-11-01 (Extended Service)
2025-09-11 16:01:09,961 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-11 16:01:11,375 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Extended Service
2025-09-11 16:01:13,393 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-11 16:01:13,393 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-11 16:01:14,423 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-11 16:01:25,954 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2023-11-01
2025-09-11 16:01:25,954 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 16:01:25,954 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:01:25,954 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-11 16:01:40,885 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-11 16:01:40,886 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/6: 2023-11-01 (Internal)
2025-09-11 16:01:40,886 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-11 16:01:42,299 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Internal
2025-09-11 16:01:44,326 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-11 16:01:44,326 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-11 16:01:45,342 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-11 16:01:57,166 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2023-11-01
2025-09-11 16:01:57,166 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 16:01:57,166 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:01:57,166 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-11 16:02:11,899 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-11 16:02:11,899 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 4/6: 2023-11-01 (Maintenance Plan)
2025-09-11 16:02:11,899 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-11 16:02:13,309 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Maintenance Plan
2025-09-11 16:02:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-11 16:02:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-11 16:02:16,370 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-11 16:02:16,392 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 4: 2023-11-01 - cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-11 16:02:16,392 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-11 16:02:31,514 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-11 16:02:31,514 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 5/6: 2023-11-01 (Warranty)
2025-09-11 16:02:31,514 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-11 16:02:32,936 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Warranty
2025-09-11 16:02:34,970 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-11 16:02:34,970 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-11 16:02:35,997 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-11 16:02:47,424 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 5: 2023-11-01
2025-09-11 16:02:47,424 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 16:02:47,424 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:02:47,424 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-11 16:03:02,441 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-11 16:03:02,442 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 6/6: 2023-11-01 (Factory Service Contract)
2025-09-11 16:03:02,442 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-11 16:03:03,886 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Factory Service Contract
2025-09-11 16:03:05,903 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-11 16:03:05,903 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-11 16:03:06,930 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-11 16:03:06,947 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 6: 2023-11-01 - cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-11 16:03:06,947 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: Labor Sold Hours Percentage By Pay Type
2025-09-11 16:03:07,040 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: CP 1-Line-RO Count (chart_948) with 3 points
2025-09-11 16:03:07,729 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_948
2025-09-11 16:03:18,859 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_948
2025-09-11 16:03:18,860 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/3: 2023-11-01 (Mileage Under 60k)
2025-09-11 16:03:18,860 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_948
2025-09-11 16:03:20,265 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_948 - Mileage Under 60k
2025-09-11 16:03:22,299 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_948 legend is active
2025-09-11 16:03:22,299 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_948 is interactive after legend control...
2025-09-11 16:03:23,322 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_948
2025-09-11 16:03:32,830 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11-01
2025-09-11 16:03:32,830 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 16:03:32,830 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:03:32,830 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-11 16:03:47,839 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-11 16:03:47,839 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/3: 2023-11-01 (Mileage Over 60k)
2025-09-11 16:03:47,839 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_948
2025-09-11 16:03:49,250 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_948 - Mileage Over 60k
2025-09-11 16:03:51,267 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_948 legend is active
2025-09-11 16:03:51,267 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_948 is interactive after legend control...
2025-09-11 16:03:52,285 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_948
2025-09-11 16:04:01,795 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2023-11-01
2025-09-11 16:04:01,795 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 16:04:01,795 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:04:01,795 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-11 16:04:16,847 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-11 16:04:16,848 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/3: 2023-11-01 (Total Shop)
2025-09-11 16:04:16,848 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_948
2025-09-11 16:04:18,255 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_948 - Total Shop
2025-09-11 16:04:20,279 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_948 legend is active
2025-09-11 16:04:20,280 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_948 is interactive after legend control...
2025-09-11 16:04:21,299 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_948
2025-09-11 16:04:30,780 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2023-11-01
2025-09-11 16:04:30,780 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 16:04:30,780 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:04:30,780 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: CP 1-Line-RO Count
2025-09-11 16:04:30,858 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: CP 1-Line-RO Count Percentage (chart_923) with 3 points
2025-09-11 16:04:31,541 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_923
2025-09-11 16:04:42,840 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_923
2025-09-11 16:04:42,841 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/3: 2023-11-01 (Mileage Under 60K)
2025-09-11 16:04:42,841 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_923
2025-09-11 16:04:44,257 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_923 - Mileage Under 60K
2025-09-11 16:04:46,275 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_923 legend is active
2025-09-11 16:04:46,275 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_923 is interactive after legend control...
2025-09-11 16:04:47,295 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_923
2025-09-11 16:04:56,773 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11-01
2025-09-11 16:04:56,773 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 16:04:56,773 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:04:56,773 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-11 16:05:11,476 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-11 16:05:11,476 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/3: 2023-11-01 (Mileage Over 60K)
2025-09-11 16:05:11,476 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_923
2025-09-11 16:05:12,889 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_923 - Mileage Over 60K
2025-09-11 16:05:14,923 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_923 legend is active
2025-09-11 16:05:14,923 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_923 is interactive after legend control...
2025-09-11 16:05:15,957 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_923
2025-09-11 16:05:25,401 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2023-11-01
2025-09-11 16:05:25,401 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 16:05:25,401 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:05:25,401 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-11 16:05:40,531 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-11 16:05:40,531 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/3: 2023-11-01 (Total Shop)
2025-09-11 16:05:40,531 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_923
2025-09-11 16:05:41,937 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_923 - Total Shop
2025-09-11 16:05:43,951 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_923 legend is active
2025-09-11 16:05:43,951 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_923 is interactive after legend control...
2025-09-11 16:05:44,966 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_923
2025-09-11 16:05:54,346 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2023-11-01
2025-09-11 16:05:54,346 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 16:05:54,346 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:05:54,346 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: CP 1-Line-RO Count Percentage
2025-09-11 16:05:54,422 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: Multi-Line-RO Count (chart_1354) with 3 points
2025-09-11 16:05:55,113 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_1354
2025-09-11 16:06:06,384 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_1354
2025-09-11 16:06:06,384 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/3: 2023-11-01 (Mileage Under 60K)
2025-09-11 16:06:06,384 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1354
2025-09-11 16:06:07,778 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1354 - Mileage Under 60K
2025-09-11 16:06:09,816 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1354 legend is active
2025-09-11 16:06:09,817 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1354 is interactive after legend control...
2025-09-11 16:06:10,847 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1354
2025-09-11 16:06:20,221 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11-01
2025-09-11 16:06:20,221 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 16:06:20,221 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1354
2025-09-11 16:06:20,221 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-11 16:06:35,172 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-11 16:06:35,173 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/3: 2023-11-01 (Mileage Over 60K)
2025-09-11 16:06:35,173 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1354
2025-09-11 16:06:36,586 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1354 - Mileage Over 60K
2025-09-11 16:06:38,615 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1354 legend is active
2025-09-11 16:06:38,615 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1354 is interactive after legend control...
2025-09-11 16:06:39,630 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1354
2025-09-11 16:06:49,002 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2023-11-01
2025-09-11 16:06:49,002 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 16:06:49,002 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1354
2025-09-11 16:06:49,002 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-11 16:07:04,069 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-11 16:07:04,069 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/3: 2023-11-01 (Total Shop)
2025-09-11 16:07:04,069 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1354
2025-09-11 16:07:05,481 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1354 - Total Shop
2025-09-11 16:07:07,507 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1354 legend is active
2025-09-11 16:07:07,507 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1354 is interactive after legend control...
2025-09-11 16:07:08,534 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1354
2025-09-11 16:07:17,845 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2023-11-01
2025-09-11 16:07:17,845 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 16:07:17,846 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1354
2025-09-11 16:07:17,846 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: Multi-Line-RO Count
2025-09-11 16:07:17,917 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: Multi-Line-RO Count Percentage (chart_1355) with 3 points
2025-09-11 16:07:18,625 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_1355
2025-09-11 16:07:30,438 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_1355
2025-09-11 16:07:30,438 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/3: 2023-11-01 (Mileage Under 60K)
2025-09-11 16:07:30,438 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1355
2025-09-11 16:07:31,848 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1355 - Mileage Under 60K
2025-09-11 16:07:33,877 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1355 legend is active
2025-09-11 16:07:33,877 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1355 is interactive after legend control...
2025-09-11 16:07:34,898 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1355
2025-09-11 16:07:44,349 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11-01
2025-09-11 16:07:44,349 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 16:07:44,349 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:07:44,349 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-11 16:07:59,311 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-11 16:07:59,312 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/3: 2023-11-01 (Mileage Over 60K)
2025-09-11 16:07:59,312 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1355
2025-09-11 16:08:00,722 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1355 - Mileage Over 60K
2025-09-11 16:08:02,751 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1355 legend is active
2025-09-11 16:08:02,751 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1355 is interactive after legend control...
2025-09-11 16:08:03,769 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1355
2025-09-11 16:08:13,208 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2023-11-01
2025-09-11 16:08:13,208 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 16:08:13,208 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:08:13,208 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-11 16:08:28,268 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-11 16:08:28,268 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/3: 2023-11-01 (Total Shop)
2025-09-11 16:08:28,268 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1355
2025-09-11 16:08:29,701 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1355 - Total Shop
2025-09-11 16:08:31,718 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1355 legend is active
2025-09-11 16:08:31,718 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1355 is interactive after legend control...
2025-09-11 16:08:32,733 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1355
2025-09-11 16:08:42,178 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2023-11-01
2025-09-11 16:08:42,178 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 16:08:42,178 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:08:42,178 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: Multi-Line-RO Count Percentage
2025-09-11 16:08:42,257 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: CP Parts to Labor Ratio By Category (chart_936) with 3 points
2025-09-11 16:08:42,926 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_936
2025-09-11 16:08:54,138 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_936
2025-09-11 16:08:54,138 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/3: 2023-11-01 (Competitive)
2025-09-11 16:08:54,138 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_936
2025-09-11 16:08:55,568 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_936 - Competitive
2025-09-11 16:08:57,591 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_936 legend is active
2025-09-11 16:08:57,591 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_936 is interactive after legend control...
2025-09-11 16:08:58,615 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_936
2025-09-11 16:09:08,206 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11-01
2025-09-11 16:09:08,206 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 16:09:08,206 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:09:08,206 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-11 16:09:23,245 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-11 16:09:23,245 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/3: 2023-11-01 (Maintenance)
2025-09-11 16:09:23,246 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_936
2025-09-11 16:09:24,659 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_936 - Maintenance
2025-09-11 16:09:26,683 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_936 legend is active
2025-09-11 16:09:26,683 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_936 is interactive after legend control...
2025-09-11 16:09:27,705 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_936
2025-09-11 16:09:37,269 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2023-11-01
2025-09-11 16:09:37,269 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 16:09:37,269 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:09:37,269 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-11 16:09:52,319 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-11 16:09:52,319 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/3: 2023-11-01 (Repair)
2025-09-11 16:09:52,320 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_936
2025-09-11 16:09:53,725 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_936 - Repair
2025-09-11 16:09:55,755 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_936 legend is active
2025-09-11 16:09:55,755 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_936 is interactive after legend control...
2025-09-11 16:09:56,773 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_936
2025-09-11 16:10:06,330 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2023-11-01
2025-09-11 16:10:06,330 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 16:10:06,330 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:10:06,330 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: CP Parts to Labor Ratio By Category
2025-09-11 16:10:06,419 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: Revenue - Shop Supplies (chart_1239) with 3 points
2025-09-11 16:10:07,140 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_1239
2025-09-11 16:10:37,149 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Error setting up chart chart_1239: Page.goto: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://ginnmotorcompany.fixedops.cc/SpecialMetrics", waiting until "load"
2025-09-11 16:10:37,273 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: CP Return Rate (chart_938) with 2 points
2025-09-11 16:10:37,951 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_938
2025-09-11 16:11:11,089 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_938
2025-09-11 16:11:11,089 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/2: 2023-11 (12 Months Return Rate)
2025-09-11 16:11:11,089 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_938
2025-09-11 16:11:12,495 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_938 - 12 Months Return Rate
2025-09-11 16:11:14,525 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_938 legend is active
2025-09-11 16:11:14,526 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_938 is interactive after legend control...
2025-09-11 16:11:15,547 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_938
2025-09-11 16:11:35,020 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 1: 2023-11 - Unknown error
2025-09-11 16:11:35,020 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-11 16:11:50,051 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-11 16:11:50,051 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/2: 2023-11 (6 Months Return Rate)
2025-09-11 16:11:50,051 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_938
2025-09-11 16:11:51,450 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_938 - 6 Months Return Rate
2025-09-11 16:11:53,474 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_938 legend is active
2025-09-11 16:11:53,474 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_938 is interactive after legend control...
2025-09-11 16:11:54,505 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_938
2025-09-11 16:12:13,979 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 2: 2023-11 - Unknown error
2025-09-11 16:12:13,980 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: CP Return Rate
2025-09-11 16:12:14,057 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: CP Parts to Labor Ratio (chart_930) with 1 points
2025-09-11 16:12:14,723 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_930
2025-09-11 16:12:25,913 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_930
2025-09-11 16:12:25,913 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/1: 2023-11-01 (Parts to Labor Ratio)
2025-09-11 16:12:25,913 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_930
2025-09-11 16:12:27,332 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_930 - Parts to Labor Ratio
2025-09-11 16:12:29,359 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_930 legend is active
2025-09-11 16:12:29,359 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_930 is interactive after legend control...
2025-09-11 16:12:30,377 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_930
2025-09-11 16:12:39,891 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11-01
2025-09-11 16:12:39,891 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 16:12:39,891 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-11 16:12:39,891 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: CP Parts to Labor Ratio
2025-09-11 16:12:39,965 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: MPI Penetration Percentage (chart_1316) with 1 points
2025-09-11 16:12:40,637 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_1316
2025-09-11 16:12:51,749 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_1316
2025-09-11 16:12:51,749 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/1: 2023-11 (MPI Penetration Percentage)
2025-09-11 16:12:51,749 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1316
2025-09-11 16:12:53,130 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1316 - MPI Penetration Percentage
2025-09-11 16:12:55,181 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1316 legend is active
2025-09-11 16:12:55,181 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1316 is interactive after legend control...
2025-09-11 16:13:16,212 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1316
2025-09-11 16:13:35,741 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11
2025-09-11 16:13:35,741 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 16:13:35,741 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1316
2025-09-11 16:13:35,741 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: MPI Penetration Percentage
2025-09-11 16:13:35,821 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: Menu Penetration Percentage (chart_1317) with 1 points
2025-09-11 16:13:36,502 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_1317
2025-09-11 16:14:02,032 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_1317
2025-09-11 16:14:02,032 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/1: 2023-11 (Menu Penetration Percentage)
2025-09-11 16:14:02,033 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1317
2025-09-11 16:14:03,429 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1317 - Menu Penetration Percentage
2025-09-11 16:14:05,470 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1317 legend is active
2025-09-11 16:14:05,471 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1317 is interactive after legend control...
2025-09-11 16:14:06,495 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1317
2025-09-11 16:14:16,620 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11
2025-09-11 16:14:16,620 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-11 16:14:16,620 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1317
2025-09-11 16:14:16,620 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: Menu Penetration Percentage
2025-09-11 16:14:16,697 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Step 3: Saving results...
2025-09-11 16:14:16,701 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Complete chart processing workflow finished successfully
2025-09-11 16:14:16,701 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Final Summary:
2025-09-11 16:14:16,701 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) - Total combinations processed: 12
2025-09-11 16:14:16,701 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) - Total tasks completed: 33
2025-09-11 16:14:16,701 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) - Success rate: 78.8%
2025-09-11 16:15:09,462 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) HTML report generated: Individual_Reports-ginnmotorcompany/cp_overview_comparison.html
