2025-09-12 15:46:19,929 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🚀 Starting complete chart processing workflow with single browser sequential processing...
2025-09-12 15:46:26,827 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Discovering charts on new component page: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 15:46:33,748 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✓ Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 15:46:33,748 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ⏳ Waiting for charts to fully load...
2025-09-12 15:46:41,793 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✓ Chart 1239 container found
2025-09-12 15:46:41,798 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✓ All charts appear to be loaded
2025-09-12 15:46:41,808 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 📊 Found 12 charts on the new component page
2025-09-12 15:46:41,808 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✅ Charts with data: 12
2025-09-12 15:46:41,808 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ❌ Charts with no data: 0
2025-09-12 15:46:41,808 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 1. Chart ID: 948 - CP 1-Line-RO Count ✅ HAS DATA
2025-09-12 15:46:41,808 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-948
2025-09-12 15:46:41,808 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_948 (Method: container-based)
2025-09-12 15:46:41,808 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 302, 'width': 777, 'height': 329}
2025-09-12 15:46:41,808 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:46:41,808 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 2. Chart ID: 1357 - Average RO Open Days ✅ HAS DATA
2025-09-12 15:46:41,808 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1357
2025-09-12 15:46:41,808 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1357 (Method: container-based)
2025-09-12 15:46:41,808 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 302, 'width': 777, 'height': 329}
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 3. Chart ID: 923 - CP 1-Line-RO Count Percentage ✅ HAS DATA
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-923
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_923 (Method: container-based)
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 702, 'width': 777, 'height': 329}
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 4. Chart ID: 1354 - Multi-Line-RO Count ✅ HAS DATA
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1354
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1354 (Method: container-based)
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 702, 'width': 777, 'height': 329}
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 5. Chart ID: 1355 - Multi-Line-RO Count Percentage ✅ HAS DATA
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1355
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1355 (Method: container-based)
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 1102, 'width': 777, 'height': 329}
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 6. Chart ID: 938 - CP Return Rate ✅ HAS DATA
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-938
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_938 (Method: container-based)
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 1102, 'width': 777, 'height': 329}
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 7. Chart ID: 930 - CP Parts to Labor Ratio ✅ HAS DATA
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-930
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_930 (Method: container-based)
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 1502, 'width': 777, 'height': 329}
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 8. Chart ID: 935 - Labor Sold Hours Percentage By Pay Type ✅ HAS DATA
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-935
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_935 (Method: container-based)
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 1502, 'width': 777, 'height': 329}
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 9. Chart ID: 936 - CP Parts to Labor Ratio By Category ✅ HAS DATA
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-936
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_936 (Method: container-based)
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 1902, 'width': 777, 'height': 329}
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 10. Chart ID: 1239 - Revenue - Shop Supplies ✅ HAS DATA
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1239
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1239 (Method: container-based)
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 1902, 'width': 777, 'height': 329}
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 11. Chart ID: 1316 - MPI Penetration Percentage ✅ HAS DATA
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1316
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1316 (Method: container-based)
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 2302, 'width': 777, 'height': 329}
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 12. Chart ID: 1317 - Menu Penetration Percentage ✅ HAS DATA
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1317
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1317 (Method: container-based)
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 2302, 'width': 777, 'height': 329}
2025-09-12 15:46:41,809 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 15:46:41,814 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) All chart container IDs on page: ['chartContainterId-948', 'chartContainterId-1357', 'chartContainterId-923', 'chartContainterId-1354', 'chartContainterId-1355', 'chartContainterId-938', 'chartContainterId-930', 'chartContainterId-935', 'chartContainterId-936', 'chartContainterId-1239', 'chartContainterId-1316', 'chartContainterId-1317']
2025-09-12 15:46:41,814 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✓ Chart 1239 found: Revenue - Shop Supplies (Index: 9)
2025-09-12 15:46:42,039 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Created 12 chart-point combinations
2025-09-12 15:46:42,039 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Step 2: Processing combinations sequentially with single browser...
2025-09-12 15:46:42,039 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart vvvvvvvvvvvvvv:({'chart_index': 'chart_1', 'chart_id': '1357', 'chart_info': {'canvasIndex': 1, 'chartTitle': 'Average RO Open Days', 'canvasId': 'chart_1357', 'canvasClass': 'chartjs-render-monitor', 'containerId': 'chartContainterId-1357', 'chartId': '1357', 'position': {'x': 1084, 'y': 302, 'width': 777, 'height': 329}, 'visible': True, 'isChartJs': True, 'hasData': True, 'detectionMethod': 'container-based', 'isFullyLoaded': True}, 'target_month_year': '2023-11-01', 'matching_points': [{'canvasIndex': 1, 'datasetIndex': 0, 'pointIndex': 10, 'value': '3.61', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 521.0641552061844, 'canvasX': 630.7514723557692, 'canvasY': 219.06415520618438, 'datasetLabel': 'Customer Pay', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 1, 'pointIndex': 10, 'value': '1.63', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 540.6757039349476, 'canvasX': 630.7514723557692, 'canvasY': 238.6757039349476, 'datasetLabel': 'Extended Service', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 2, 'pointIndex': 10, 'value': '5.40', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 503.3345227695752, 'canvasX': 630.7514723557692, 'canvasY': 201.33452276957522, 'datasetLabel': 'Internal', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 3, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 630.7514723557692, 'canvasY': nan, 'datasetLabel': 'Maintenance', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 4, 'pointIndex': 10, 'value': '6.93', 'xLabel': '2023-11-01', 'screenX': 1714.7514723557692, 'screenY': 488.18014420644, 'canvasX': 630.7514723557692, 'canvasY': 186.18014420644, 'datasetLabel': 'Warranty', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 1, 'datasetIndex': 5, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 630.7514723557692, 'canvasY': nan, 'datasetLabel': 'Factory Service Contract', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}], 'processing_status': 'pending', 'points_count': 6}) with 6 points
2025-09-12 15:46:42,738 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_1357
2025-09-12 15:46:59,924 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_1357
2025-09-12 15:46:59,924 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/6: 2023-11-01 (Customer Pay)
2025-09-12 15:46:59,924 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-12 15:47:01,330 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Customer Pay
2025-09-12 15:47:03,347 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-12 15:47:03,347 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-12 15:47:04,379 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-12 15:47:13,767 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11-01
2025-09-12 15:47:13,767 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 15:47:13,767 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 15:47:13,767 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 15:47:28,705 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 15:47:28,705 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/6: 2023-11-01 (Extended Service)
2025-09-12 15:47:28,705 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-12 15:47:30,143 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Extended Service
2025-09-12 15:47:32,162 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-12 15:47:32,162 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-12 15:47:33,178 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-12 15:47:42,502 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2023-11-01
2025-09-12 15:47:42,502 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 15:47:42,502 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 15:47:42,502 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 15:47:57,442 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 15:47:57,442 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/6: 2023-11-01 (Internal)
2025-09-12 15:47:57,442 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-12 15:47:58,871 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Internal
2025-09-12 15:48:00,907 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-12 15:48:00,907 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-12 15:48:01,935 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-12 15:48:11,278 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2023-11-01
2025-09-12 15:48:11,278 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 15:48:11,278 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 15:48:11,278 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 15:48:26,357 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 15:48:26,357 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 4/6: 2023-11-01 (Maintenance)
2025-09-12 15:48:26,357 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-12 15:48:27,785 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Maintenance
2025-09-12 15:48:29,801 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-12 15:48:29,801 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-12 15:48:30,830 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-12 15:48:30,846 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 4: 2023-11-01 - cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 15:48:30,846 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 15:48:45,831 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 15:48:45,831 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 5/6: 2023-11-01 (Warranty)
2025-09-12 15:48:45,831 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-12 15:48:47,243 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Warranty
2025-09-12 15:48:49,269 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-12 15:48:49,269 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-12 15:48:50,303 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-12 15:48:59,696 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 5: 2023-11-01
2025-09-12 15:48:59,697 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 15:48:59,697 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 15:48:59,697 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 15:49:14,714 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 15:49:14,714 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 6/6: 2023-11-01 (Factory Service Contract)
2025-09-12 15:49:14,715 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-12 15:49:16,171 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Factory Service Contract
2025-09-12 15:49:18,187 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-12 15:49:18,187 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-12 15:49:19,221 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-12 15:49:19,239 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 6: 2023-11-01 - cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 15:49:19,239 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: Average RO Open Days
2025-09-12 15:49:19,332 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart vvvvvvvvvvvvvv:({'chart_index': 'chart_7', 'chart_id': '935', 'chart_info': {'canvasIndex': 7, 'chartTitle': 'Labor Sold Hours Percentage By Pay Type', 'canvasId': 'chart_935', 'canvasClass': 'chartjs-render-monitor', 'containerId': 'chartContainterId-935', 'chartId': '935', 'position': {'x': 1084, 'y': 1502, 'width': 777, 'height': 329}, 'visible': True, 'isChartJs': True, 'hasData': True, 'detectionMethod': 'container-based', 'isFullyLoaded': True}, 'target_month_year': '2023-11-01', 'matching_points': [{'canvasIndex': 7, 'datasetIndex': 0, 'pointIndex': 10, 'value': '0.57', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1639.2007964974835, 'canvasX': 634.727959735577, 'canvasY': 137.2007964974834, 'datasetLabel': 'Customer Pay', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 1, 'pointIndex': 10, 'value': '0.05', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1746.5030412255649, 'canvasX': 634.727959735577, 'canvasY': 244.50304122556494, 'datasetLabel': 'Extended Service', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 2, 'pointIndex': 10, 'value': '0.18', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1719.6774800435446, 'canvasX': 634.727959735577, 'canvasY': 217.67748004354456, 'datasetLabel': 'Internal', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 3, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 634.727959735577, 'canvasY': nan, 'datasetLabel': 'Maintenance Plan', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 4, 'pointIndex': 10, 'value': '0.19', 'xLabel': '2023-11-01', 'screenX': 1718.7279597355769, 'screenY': 1717.6139753372354, 'canvasX': 634.727959735577, 'canvasY': 215.6139753372353, 'datasetLabel': 'Warranty', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 7, 'datasetIndex': 5, 'pointIndex': 10, 'value': None, 'xLabel': '2023-11-01', 'screenX': None, 'screenY': None, 'canvasX': 634.727959735577, 'canvasY': nan, 'datasetLabel': 'Factory Service Contract', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2023-11-01'}], 'processing_status': 'pending', 'points_count': 6}) with 6 points
2025-09-12 15:49:20,068 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_935
2025-09-12 15:49:32,164 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_935
2025-09-12 15:49:32,164 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/6: 2023-11-01 (Customer Pay)
2025-09-12 15:49:32,164 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-12 15:49:33,604 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Customer Pay
2025-09-12 15:49:35,627 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-12 15:49:35,627 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-12 15:49:36,647 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-12 15:49:48,905 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11-01
2025-09-12 15:49:48,905 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 15:49:48,905 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:49:48,905 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 15:50:03,998 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 15:50:03,999 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/6: 2023-11-01 (Extended Service)
2025-09-12 15:50:03,999 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-12 15:50:05,418 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Extended Service
2025-09-12 15:50:07,467 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-12 15:50:07,467 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-12 15:50:08,498 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-12 15:50:23,701 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2023-11-01
2025-09-12 15:50:23,701 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 15:50:23,701 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:50:23,701 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 15:50:38,807 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 15:50:38,807 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/6: 2023-11-01 (Internal)
2025-09-12 15:50:38,807 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-12 15:50:40,245 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Internal
2025-09-12 15:50:42,274 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-12 15:50:42,274 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-12 15:50:43,314 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-12 15:50:58,156 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2023-11-01
2025-09-12 15:50:58,156 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 15:50:58,157 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:50:58,157 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 15:51:13,284 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 15:51:13,284 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 4/6: 2023-11-01 (Maintenance Plan)
2025-09-12 15:51:13,285 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-12 15:51:14,759 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Maintenance Plan
2025-09-12 15:51:16,781 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-12 15:51:16,781 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-12 15:51:17,815 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-12 15:51:17,842 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 4: 2023-11-01 - cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 15:51:17,842 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 15:51:33,301 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 15:51:33,301 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 5/6: 2023-11-01 (Warranty)
2025-09-12 15:51:33,301 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-12 15:51:34,754 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Warranty
2025-09-12 15:51:36,779 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-12 15:51:36,779 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-12 15:51:37,807 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-12 15:51:50,414 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 5: 2023-11-01
2025-09-12 15:51:50,414 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 15:51:50,414 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:51:50,414 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 15:52:05,198 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 15:52:05,198 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 6/6: 2023-11-01 (Factory Service Contract)
2025-09-12 15:52:05,198 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-12 15:52:06,621 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Factory Service Contract
2025-09-12 15:52:08,658 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-12 15:52:08,658 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-12 15:52:09,687 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-12 15:52:09,694 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 6: 2023-11-01 - cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 15:52:09,694 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: Labor Sold Hours Percentage By Pay Type
2025-09-12 15:52:09,787 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart vvvvvvvvvvvvvv:({'chart_index': 'chart_0', 'chart_id': '948', 'chart_info': {'canvasIndex': 0, 'chartTitle': 'CP 1-Line-RO Count', 'canvasId': 'chart_948', 'canvasClass': 'chartjs-render-monitor', 'containerId': 'chartContainterId-948', 'chartId': '948', 'position': {'x': 275, 'y': 302, 'width': 777, 'height': 329}, 'visible': True, 'isChartJs': True, 'hasData': True, 'detectionMethod': 'container-based', 'isFullyLoaded': True}, 'target_month_year': '2023-11-01', 'matching_points': [{'canvasIndex': 0, 'datasetIndex': 0, 'pointIndex': 10, 'value': '188', 'xLabel': '2023-11-01', 'screenX': 907.0349008413461, 'screenY': 463.7152324084374, 'canvasX': 632.0349008413461, 'canvasY': 161.7152324084374, 'datasetLabel': 'Mileage Under 60k', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 0, 'datasetIndex': 1, 'pointIndex': 10, 'value': '215', 'xLabel': '2023-11-01', 'screenX': 907.0349008413461, 'screenY': 450.3437219115534, 'canvasX': 632.0349008413461, 'canvasY': 148.34372191155342, 'datasetLabel': 'Mileage Over 60k', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 0, 'datasetIndex': 2, 'pointIndex': 10, 'value': '403', 'xLabel': '2023-11-01', 'screenX': 907.0349008413461, 'screenY': 357.23838956287955, 'canvasX': 632.0349008413461, 'canvasY': 55.238389562879576, 'datasetLabel': 'Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}], 'processing_status': 'pending', 'points_count': 3}) with 3 points
2025-09-12 15:52:10,486 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_948
2025-09-12 15:52:22,377 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_948
2025-09-12 15:52:22,377 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/3: 2023-11-01 (Mileage Under 60k)
2025-09-12 15:52:22,377 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_948
2025-09-12 15:52:23,750 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_948 - Mileage Under 60k
2025-09-12 15:52:25,777 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_948 legend is active
2025-09-12 15:52:25,777 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_948 is interactive after legend control...
2025-09-12 15:52:26,794 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_948
2025-09-12 15:52:36,306 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11-01
2025-09-12 15:52:36,307 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 15:52:36,307 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:52:36,307 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 15:52:51,051 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 15:52:51,051 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/3: 2023-11-01 (Mileage Over 60k)
2025-09-12 15:52:51,051 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_948
2025-09-12 15:52:52,435 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_948 - Mileage Over 60k
2025-09-12 15:52:54,463 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_948 legend is active
2025-09-12 15:52:54,463 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_948 is interactive after legend control...
2025-09-12 15:52:55,491 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_948
2025-09-12 15:53:04,985 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2023-11-01
2025-09-12 15:53:04,985 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 15:53:04,986 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:53:04,986 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 15:53:19,772 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 15:53:19,773 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/3: 2023-11-01 (Total Shop)
2025-09-12 15:53:19,773 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_948
2025-09-12 15:53:21,141 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_948 - Total Shop
2025-09-12 15:53:23,167 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_948 legend is active
2025-09-12 15:53:23,167 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_948 is interactive after legend control...
2025-09-12 15:53:24,193 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_948
2025-09-12 15:53:33,694 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2023-11-01
2025-09-12 15:53:33,694 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 15:53:33,694 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:53:33,694 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: CP 1-Line-RO Count
2025-09-12 15:53:33,768 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart vvvvvvvvvvvvvv:({'chart_index': 'chart_2', 'chart_id': '923', 'chart_info': {'canvasIndex': 2, 'chartTitle': 'CP 1-Line-RO Count Percentage', 'canvasId': 'chart_923', 'canvasClass': 'chartjs-render-monitor', 'containerId': 'chartContainterId-923', 'chartId': '923', 'position': {'x': 275, 'y': 702, 'width': 777, 'height': 329}, 'visible': True, 'isChartJs': True, 'hasData': True, 'detectionMethod': 'container-based', 'isFullyLoaded': True}, 'target_month_year': '2023-11-01', 'matching_points': [{'canvasIndex': 2, 'datasetIndex': 0, 'pointIndex': 10, 'value': '70.15', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 739.6882820357193, 'canvasX': 633.44453125, 'canvasY': 37.688282035719304, 'datasetLabel': 'Mileage Under 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 2, 'datasetIndex': 1, 'pointIndex': 10, 'value': '67.40', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 748.200238949245, 'canvasX': 633.44453125, 'canvasY': 46.20023894924502, 'datasetLabel': 'Mileage Over 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 2, 'datasetIndex': 2, 'pointIndex': 10, 'value': '68.65', 'xLabel': '2023-11-01', 'screenX': 908.44453125, 'screenY': 744.3311676249151, 'canvasX': 633.44453125, 'canvasY': 42.33116762491515, 'datasetLabel': 'Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}], 'processing_status': 'pending', 'points_count': 3}) with 3 points
2025-09-12 15:53:34,525 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_923
2025-09-12 15:53:46,963 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_923
2025-09-12 15:53:46,963 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/3: 2023-11-01 (Mileage Under 60K)
2025-09-12 15:53:46,963 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_923
2025-09-12 15:53:48,337 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_923 - Mileage Under 60K
2025-09-12 15:53:50,358 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_923 legend is active
2025-09-12 15:53:50,359 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_923 is interactive after legend control...
2025-09-12 15:53:51,403 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_923
2025-09-12 15:54:00,912 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11-01
2025-09-12 15:54:00,912 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 15:54:00,913 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:54:00,913 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 15:54:15,993 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 15:54:15,993 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/3: 2023-11-01 (Mileage Over 60K)
2025-09-12 15:54:15,993 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_923
2025-09-12 15:54:17,387 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_923 - Mileage Over 60K
2025-09-12 15:54:19,406 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_923 legend is active
2025-09-12 15:54:19,406 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_923 is interactive after legend control...
2025-09-12 15:54:20,433 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_923
2025-09-12 15:54:29,815 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2023-11-01
2025-09-12 15:54:29,815 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 15:54:29,815 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:54:29,815 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 15:54:44,814 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 15:54:44,815 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/3: 2023-11-01 (Total Shop)
2025-09-12 15:54:44,815 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_923
2025-09-12 15:54:46,235 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_923 - Total Shop
2025-09-12 15:54:48,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_923 legend is active
2025-09-12 15:54:48,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_923 is interactive after legend control...
2025-09-12 15:54:49,279 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_923
2025-09-12 15:54:58,673 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2023-11-01
2025-09-12 15:54:58,673 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 15:54:58,673 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 15:54:58,673 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: CP 1-Line-RO Count Percentage
2025-09-12 15:54:58,746 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart vvvvvvvvvvvvvv:({'chart_index': 'chart_3', 'chart_id': '1354', 'chart_info': {'canvasIndex': 3, 'chartTitle': 'Multi-Line-RO Count', 'canvasId': 'chart_1354', 'canvasClass': 'chartjs-render-monitor', 'containerId': 'chartContainterId-1354', 'chartId': '1354', 'position': {'x': 1084, 'y': 702, 'width': 777, 'height': 329}, 'visible': True, 'isChartJs': True, 'hasData': True, 'detectionMethod': 'container-based', 'isFullyLoaded': True}, 'target_month_year': '2023-11-01', 'matching_points': [{'canvasIndex': 3, 'datasetIndex': 0, 'pointIndex': 10, 'value': '80', 'xLabel': '2023-11-01', 'screenX': 1716.0349008413461, 'screenY': 877.5819840348356, 'canvasX': 632.0349008413461, 'canvasY': 175.5819840348356, 'datasetLabel': 'Mileage Under 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 3, 'datasetIndex': 1, 'pointIndex': 10, 'value': '104', 'xLabel': '2023-11-01', 'screenX': 1716.0349008413461, 'screenY': 853.810409818153, 'canvasX': 632.0349008413461, 'canvasY': 151.81040981815298, 'datasetLabel': 'Mileage Over 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}, {'canvasIndex': 3, 'datasetIndex': 2, 'pointIndex': 10, 'value': '184', 'xLabel': '2023-11-01', 'screenX': 1716.0349008413461, 'screenY': 774.5718290958773, 'canvasX': 632.0349008413461, 'canvasY': 72.57182909587738, 'datasetLabel': 'Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2023-11-01'}], 'processing_status': 'pending', 'points_count': 3}) with 3 points
2025-09-12 15:54:59,483 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_1354
