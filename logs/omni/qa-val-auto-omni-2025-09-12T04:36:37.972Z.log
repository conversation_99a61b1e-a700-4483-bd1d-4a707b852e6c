2025-09-12 10:06:47,883 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🚀 Starting complete chart processing workflow with single browser sequential processing...
2025-09-12 10:06:54,747 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Discovering charts on new component page: https://ginnmotorcompany.fixedops.cc/SpecialMetrics
2025-09-12 10:07:07,291 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✓ Found charts using selector: canvas.chartjs-render-monitor
2025-09-12 10:07:07,292 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ⏳ Waiting for charts to fully load...
2025-09-12 10:07:15,322 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✓ Chart 1239 container found
2025-09-12 10:07:15,327 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✓ All charts appear to be loaded
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 📊 Found 12 charts on the new component page
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✅ Charts with data: 12
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ❌ Charts with no data: 0
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 1. Chart ID: 948 - CP 1-Line-RO Count ✅ HAS DATA
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-948
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_948 (Method: container-based)
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 302, 'width': 777, 'height': 329}
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 2. Chart ID: 1357 - Average RO Open Days ✅ HAS DATA
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1357
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1357 (Method: container-based)
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 302, 'width': 777, 'height': 329}
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 3. Chart ID: 923 - CP 1-Line-RO Count Percentage ✅ HAS DATA
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-923
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_923 (Method: container-based)
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 702, 'width': 777, 'height': 329}
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 4. Chart ID: 1354 - Multi-Line-RO Count ✅ HAS DATA
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1354
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1354 (Method: container-based)
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 702, 'width': 777, 'height': 329}
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 5. Chart ID: 1355 - Multi-Line-RO Count Percentage ✅ HAS DATA
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1355
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1355 (Method: container-based)
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 1102, 'width': 777, 'height': 329}
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 6. Chart ID: 938 - CP Return Rate ✅ HAS DATA
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-938
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_938 (Method: container-based)
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 1102, 'width': 777, 'height': 329}
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 7. Chart ID: 930 - CP Parts to Labor Ratio ✅ HAS DATA
2025-09-12 10:07:15,338 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-930
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_930 (Method: container-based)
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 1502, 'width': 777, 'height': 329}
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 8. Chart ID: 935 - Labor Sold Hours Percentage By Pay Type ✅ HAS DATA
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-935
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_935 (Method: container-based)
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 1502, 'width': 777, 'height': 329}
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 9. Chart ID: 936 - CP Parts to Labor Ratio By Category ✅ HAS DATA
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-936
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_936 (Method: container-based)
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 1902, 'width': 777, 'height': 329}
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 10. Chart ID: 1239 - Revenue - Shop Supplies ✅ HAS DATA
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1239
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1239 (Method: container-based)
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 1902, 'width': 777, 'height': 329}
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 11. Chart ID: 1316 - MPI Penetration Percentage ✅ HAS DATA
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1316
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1316 (Method: container-based)
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 2302, 'width': 777, 'height': 329}
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 12. Chart ID: 1317 - Menu Penetration Percentage ✅ HAS DATA
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1317
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1317 (Method: container-based)
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 2302, 'width': 777, 'height': 329}
2025-09-12 10:07:15,339 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-12 10:07:15,343 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) All chart container IDs on page: ['chartContainterId-948', 'chartContainterId-1357', 'chartContainterId-923', 'chartContainterId-1354', 'chartContainterId-1355', 'chartContainterId-938', 'chartContainterId-930', 'chartContainterId-935', 'chartContainterId-936', 'chartContainterId-1239', 'chartContainterId-1316', 'chartContainterId-1317']
2025-09-12 10:07:15,343 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✓ Chart 1239 found: Revenue - Shop Supplies (Index: 9)
2025-09-12 10:07:15,615 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Created 12 chart-point combinations
2025-09-12 10:07:15,615 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Step 2: Processing combinations sequentially with single browser...
2025-09-12 10:07:15,615 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: Average RO Open Days (chart_1357) with 6 points
2025-09-12 10:07:16,392 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_1357
2025-09-12 10:07:28,080 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_1357
2025-09-12 10:07:28,080 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/6: 2023-11-01 (Customer Pay)
2025-09-12 10:07:28,080 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-12 10:07:29,471 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Customer Pay
2025-09-12 10:07:31,507 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-12 10:07:31,507 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-12 10:07:32,537 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-12 10:07:41,913 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11-01
2025-09-12 10:07:41,913 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:07:41,913 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 10:07:41,913 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 10:07:56,858 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 10:07:56,858 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/6: 2023-11-01 (Extended Service)
2025-09-12 10:07:56,859 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-12 10:07:58,277 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Extended Service
2025-09-12 10:08:00,308 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-12 10:08:00,308 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-12 10:08:01,341 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-12 10:08:10,770 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2023-11-01
2025-09-12 10:08:10,770 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:08:10,770 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 10:08:10,770 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 10:08:26,039 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 10:08:26,039 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/6: 2023-11-01 (Internal)
2025-09-12 10:08:26,039 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-12 10:08:27,518 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Internal
2025-09-12 10:08:29,556 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-12 10:08:29,556 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-12 10:08:30,595 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-12 10:08:39,989 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2023-11-01
2025-09-12 10:08:39,989 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:08:39,989 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 10:08:39,990 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 10:08:59,687 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 10:08:59,687 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 4/6: 2023-11-01 (Maintenance)
2025-09-12 10:08:59,687 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-12 10:09:01,090 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Maintenance
2025-09-12 10:09:03,119 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-12 10:09:03,119 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-12 10:09:04,144 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-12 10:09:04,173 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 4: 2023-11-01 - cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 10:09:04,173 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 10:09:19,279 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 10:09:19,279 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 5/6: 2023-11-01 (Warranty)
2025-09-12 10:09:19,280 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-12 10:09:20,697 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Warranty
2025-09-12 10:09:22,718 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-12 10:09:22,718 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-12 10:09:23,737 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-12 10:09:33,088 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 5: 2023-11-01
2025-09-12 10:09:33,088 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:09:33,088 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1357
2025-09-12 10:09:33,088 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 10:09:48,103 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 10:09:48,103 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 6/6: 2023-11-01 (Factory Service Contract)
2025-09-12 10:09:48,103 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-12 10:09:49,509 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Factory Service Contract
2025-09-12 10:09:51,542 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-12 10:09:51,542 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-12 10:09:52,563 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-12 10:09:52,571 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 6: 2023-11-01 - cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 10:09:52,571 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: Average RO Open Days
2025-09-12 10:09:52,667 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: Labor Sold Hours Percentage By Pay Type (chart_935) with 6 points
2025-09-12 10:09:53,386 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_935
2025-09-12 10:10:07,564 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_935
2025-09-12 10:10:07,564 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/6: 2023-11-01 (Customer Pay)
2025-09-12 10:10:07,564 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-12 10:10:09,007 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Customer Pay
2025-09-12 10:10:11,028 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-12 10:10:11,028 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-12 10:10:12,051 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-12 10:10:24,731 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11-01
2025-09-12 10:10:24,731 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:10:24,731 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:10:24,731 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 10:10:39,698 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 10:10:39,698 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/6: 2023-11-01 (Extended Service)
2025-09-12 10:10:39,698 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-12 10:10:41,127 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Extended Service
2025-09-12 10:10:43,161 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-12 10:10:43,162 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-12 10:10:44,190 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-12 10:10:56,181 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2023-11-01
2025-09-12 10:10:56,181 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:10:56,181 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:10:56,181 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 10:11:10,957 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 10:11:10,957 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/6: 2023-11-01 (Internal)
2025-09-12 10:11:10,957 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-12 10:11:12,401 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Internal
2025-09-12 10:11:14,438 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-12 10:11:14,438 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-12 10:11:15,457 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-12 10:11:27,783 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2023-11-01
2025-09-12 10:11:27,783 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:11:27,783 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:11:27,783 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 10:11:42,780 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 10:11:42,780 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 4/6: 2023-11-01 (Maintenance Plan)
2025-09-12 10:11:42,780 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-12 10:11:44,219 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Maintenance Plan
2025-09-12 10:11:46,248 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-12 10:11:46,248 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-12 10:11:47,280 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-12 10:11:47,286 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 4: 2023-11-01 - cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 10:11:47,286 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 10:12:02,618 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 10:12:02,618 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 5/6: 2023-11-01 (Warranty)
2025-09-12 10:12:02,618 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-12 10:12:04,007 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Warranty
2025-09-12 10:12:06,027 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-12 10:12:06,027 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-12 10:12:07,051 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-12 10:12:19,094 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 5: 2023-11-01
2025-09-12 10:12:19,094 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:12:19,094 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:12:19,094 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 10:12:34,149 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 10:12:34,149 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 6/6: 2023-11-01 (Factory Service Contract)
2025-09-12 10:12:34,149 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-12 10:12:35,583 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Factory Service Contract
2025-09-12 10:12:37,611 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-12 10:12:37,612 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-12 10:12:38,646 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-12 10:12:38,658 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 6: 2023-11-01 - cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 10:12:38,658 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: Labor Sold Hours Percentage By Pay Type
2025-09-12 10:12:38,765 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: CP 1-Line-RO Count (chart_948) with 3 points
2025-09-12 10:12:39,548 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_948
2025-09-12 10:12:51,027 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_948
2025-09-12 10:12:51,027 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/3: 2023-11-01 (Mileage Under 60k)
2025-09-12 10:12:51,027 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_948
2025-09-12 10:12:52,423 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_948 - Mileage Under 60k
2025-09-12 10:12:54,459 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_948 legend is active
2025-09-12 10:12:54,459 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_948 is interactive after legend control...
2025-09-12 10:12:55,483 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_948
2025-09-12 10:13:05,073 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11-01
2025-09-12 10:13:05,073 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:13:05,073 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:13:05,073 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 10:13:19,793 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 10:13:19,793 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/3: 2023-11-01 (Mileage Over 60k)
2025-09-12 10:13:19,793 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_948
2025-09-12 10:13:21,200 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_948 - Mileage Over 60k
2025-09-12 10:13:23,219 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_948 legend is active
2025-09-12 10:13:23,219 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_948 is interactive after legend control...
2025-09-12 10:13:24,251 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_948
2025-09-12 10:13:33,759 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2023-11-01
2025-09-12 10:13:33,759 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:13:33,759 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:13:33,759 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 10:13:48,705 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 10:13:48,706 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/3: 2023-11-01 (Total Shop)
2025-09-12 10:13:48,706 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_948
2025-09-12 10:13:50,143 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_948 - Total Shop
2025-09-12 10:13:52,178 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_948 legend is active
2025-09-12 10:13:52,178 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_948 is interactive after legend control...
2025-09-12 10:13:53,198 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_948
2025-09-12 10:14:02,755 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2023-11-01
2025-09-12 10:14:02,755 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:14:02,755 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:14:02,755 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: CP 1-Line-RO Count
2025-09-12 10:14:02,836 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: CP 1-Line-RO Count Percentage (chart_923) with 3 points
2025-09-12 10:14:03,638 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_923
2025-09-12 10:14:15,407 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_923
2025-09-12 10:14:15,407 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/3: 2023-11-01 (Mileage Under 60K)
2025-09-12 10:14:15,408 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_923
2025-09-12 10:14:16,789 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_923 - Mileage Under 60K
2025-09-12 10:14:18,811 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_923 legend is active
2025-09-12 10:14:18,811 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_923 is interactive after legend control...
2025-09-12 10:14:19,837 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_923
2025-09-12 10:14:29,336 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11-01
2025-09-12 10:14:29,336 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:14:29,336 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:14:29,336 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 10:14:44,333 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 10:14:44,333 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/3: 2023-11-01 (Mileage Over 60K)
2025-09-12 10:14:44,333 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_923
2025-09-12 10:14:45,759 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_923 - Mileage Over 60K
2025-09-12 10:14:47,787 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_923 legend is active
2025-09-12 10:14:47,787 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_923 is interactive after legend control...
2025-09-12 10:14:48,813 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_923
2025-09-12 10:14:58,294 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2023-11-01
2025-09-12 10:14:58,294 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:14:58,294 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:14:58,294 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 10:15:13,365 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 10:15:13,366 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/3: 2023-11-01 (Total Shop)
2025-09-12 10:15:13,366 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_923
2025-09-12 10:15:14,818 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_923 - Total Shop
2025-09-12 10:15:16,853 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_923 legend is active
2025-09-12 10:15:16,853 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_923 is interactive after legend control...
2025-09-12 10:15:17,869 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_923
2025-09-12 10:15:27,302 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2023-11-01
2025-09-12 10:15:27,302 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:15:27,302 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:15:27,302 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: CP 1-Line-RO Count Percentage
2025-09-12 10:15:27,390 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: Multi-Line-RO Count (chart_1354) with 3 points
2025-09-12 10:15:28,107 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_1354
2025-09-12 10:15:39,457 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_1354
2025-09-12 10:15:39,457 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/3: 2023-11-01 (Mileage Under 60K)
2025-09-12 10:15:39,457 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1354
2025-09-12 10:15:40,859 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1354 - Mileage Under 60K
2025-09-12 10:15:42,896 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1354 legend is active
2025-09-12 10:15:42,896 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1354 is interactive after legend control...
2025-09-12 10:15:43,911 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1354
2025-09-12 10:15:53,337 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11-01
2025-09-12 10:15:53,337 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:15:53,337 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1354
2025-09-12 10:15:53,337 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 10:16:08,321 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 10:16:08,322 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/3: 2023-11-01 (Mileage Over 60K)
2025-09-12 10:16:08,322 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1354
2025-09-12 10:16:09,702 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1354 - Mileage Over 60K
2025-09-12 10:16:11,731 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1354 legend is active
2025-09-12 10:16:11,731 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1354 is interactive after legend control...
2025-09-12 10:16:12,754 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1354
2025-09-12 10:16:22,076 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2023-11-01
2025-09-12 10:16:22,076 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:16:22,076 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1354
2025-09-12 10:16:22,076 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 10:16:37,114 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 10:16:37,115 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/3: 2023-11-01 (Total Shop)
2025-09-12 10:16:37,115 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1354
2025-09-12 10:16:38,516 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1354 - Total Shop
2025-09-12 10:16:40,547 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1354 legend is active
2025-09-12 10:16:40,547 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1354 is interactive after legend control...
2025-09-12 10:16:41,565 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1354
2025-09-12 10:16:50,909 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2023-11-01
2025-09-12 10:16:50,909 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:16:50,909 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1354
2025-09-12 10:16:50,909 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: Multi-Line-RO Count
2025-09-12 10:16:50,981 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: Multi-Line-RO Count Percentage (chart_1355) with 3 points
2025-09-12 10:16:51,674 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_1355
2025-09-12 10:17:04,104 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_1355
2025-09-12 10:17:04,104 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/3: 2023-11-01 (Mileage Under 60K)
2025-09-12 10:17:04,104 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1355
2025-09-12 10:17:05,498 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1355 - Mileage Under 60K
2025-09-12 10:17:07,523 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1355 legend is active
2025-09-12 10:17:07,523 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1355 is interactive after legend control...
2025-09-12 10:17:08,543 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1355
2025-09-12 10:17:18,033 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11-01
2025-09-12 10:17:18,033 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:17:18,033 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:17:18,033 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 10:17:32,781 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 10:17:32,781 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/3: 2023-11-01 (Mileage Over 60K)
2025-09-12 10:17:32,781 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1355
2025-09-12 10:17:34,207 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1355 - Mileage Over 60K
2025-09-12 10:17:36,240 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1355 legend is active
2025-09-12 10:17:36,240 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1355 is interactive after legend control...
2025-09-12 10:17:37,262 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1355
2025-09-12 10:17:46,721 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2023-11-01
2025-09-12 10:17:46,721 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:17:46,721 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:17:46,721 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 10:18:01,759 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 10:18:01,759 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/3: 2023-11-01 (Total Shop)
2025-09-12 10:18:01,759 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1355
2025-09-12 10:18:03,131 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1355 - Total Shop
2025-09-12 10:18:05,163 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1355 legend is active
2025-09-12 10:18:05,163 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1355 is interactive after legend control...
2025-09-12 10:18:06,183 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1355
2025-09-12 10:18:15,603 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2023-11-01
2025-09-12 10:18:15,603 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:18:15,603 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:18:15,603 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: Multi-Line-RO Count Percentage
2025-09-12 10:18:15,679 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: CP Parts to Labor Ratio By Category (chart_936) with 3 points
2025-09-12 10:18:16,386 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_936
2025-09-12 10:18:27,624 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_936
2025-09-12 10:18:27,625 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/3: 2023-11-01 (Competitive)
2025-09-12 10:18:27,625 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_936
2025-09-12 10:18:29,031 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_936 - Competitive
2025-09-12 10:18:31,059 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_936 legend is active
2025-09-12 10:18:31,059 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_936 is interactive after legend control...
2025-09-12 10:18:32,079 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_936
2025-09-12 10:18:41,618 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11-01
2025-09-12 10:18:41,618 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:18:41,618 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:18:41,618 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 10:18:56,603 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 10:18:56,603 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/3: 2023-11-01 (Maintenance)
2025-09-12 10:18:56,603 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_936
2025-09-12 10:18:57,978 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_936 - Maintenance
2025-09-12 10:19:00,002 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_936 legend is active
2025-09-12 10:19:00,002 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_936 is interactive after legend control...
2025-09-12 10:19:01,026 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_936
2025-09-12 10:19:10,609 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2023-11-01
2025-09-12 10:19:10,609 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:19:10,609 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:19:10,609 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 10:19:25,587 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 10:19:25,587 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/3: 2023-11-01 (Repair)
2025-09-12 10:19:25,587 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_936
2025-09-12 10:19:26,983 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_936 - Repair
2025-09-12 10:19:29,015 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_936 legend is active
2025-09-12 10:19:29,015 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_936 is interactive after legend control...
2025-09-12 10:19:30,035 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_936
2025-09-12 10:19:39,541 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2023-11-01
2025-09-12 10:19:39,541 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:19:39,541 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:19:39,541 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: CP Parts to Labor Ratio By Category
2025-09-12 10:19:39,615 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: Revenue - Shop Supplies (chart_1239) with 3 points
2025-09-12 10:19:40,305 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_1239
2025-09-12 10:19:52,039 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_1239
2025-09-12 10:19:52,039 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/3: 2023-11-01 (Customer Pay)
2025-09-12 10:19:52,039 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1239
2025-09-12 10:19:53,451 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1239 - Customer Pay
2025-09-12 10:19:55,475 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1239 legend is active
2025-09-12 10:19:55,475 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1239 is interactive after legend control...
2025-09-12 10:19:56,499 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1239
2025-09-12 10:19:56,513 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 1: 2023-11-01 - cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 10:19:56,513 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 10:20:11,214 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 10:20:11,214 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/3: 2023-11-01 (Warranty)
2025-09-12 10:20:11,214 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1239
2025-09-12 10:20:12,599 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1239 - Warranty
2025-09-12 10:20:14,634 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1239 legend is active
2025-09-12 10:20:14,634 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1239 is interactive after legend control...
2025-09-12 10:20:15,669 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1239
2025-09-12 10:20:15,695 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 2: 2023-11-01 - cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 10:20:15,695 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 10:20:30,781 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 10:20:30,782 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/3: 2023-11-01 (Internal)
2025-09-12 10:20:30,782 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1239
2025-09-12 10:20:32,158 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1239 - Internal
2025-09-12 10:20:34,187 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1239 legend is active
2025-09-12 10:20:34,187 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1239 is interactive after legend control...
2025-09-12 10:20:35,214 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1239
2025-09-12 10:20:35,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 3: 2023-11-01 - cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-12 10:20:35,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: Revenue - Shop Supplies
2025-09-12 10:20:35,353 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: CP Return Rate (chart_938) with 2 points
2025-09-12 10:20:36,105 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_938
2025-09-12 10:20:47,548 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_938
2025-09-12 10:20:47,548 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/2: 2023-11 (12 Months Return Rate)
2025-09-12 10:20:47,548 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_938
2025-09-12 10:20:48,967 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_938 - 12 Months Return Rate
2025-09-12 10:20:50,990 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_938 legend is active
2025-09-12 10:20:50,990 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_938 is interactive after legend control...
2025-09-12 10:20:52,014 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_938
2025-09-12 10:21:11,531 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 1: 2023-11 - Unknown error
2025-09-12 10:21:11,531 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-12 10:21:26,474 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-12 10:21:26,474 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/2: 2023-11 (6 Months Return Rate)
2025-09-12 10:21:26,475 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_938
2025-09-12 10:21:27,878 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_938 - 6 Months Return Rate
2025-09-12 10:21:29,917 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_938 legend is active
2025-09-12 10:21:29,917 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_938 is interactive after legend control...
2025-09-12 10:21:30,934 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_938
2025-09-12 10:21:50,422 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 2: 2023-11 - Unknown error
2025-09-12 10:21:50,422 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: CP Return Rate
2025-09-12 10:21:50,497 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: CP Parts to Labor Ratio (chart_930) with 1 points
2025-09-12 10:21:51,207 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_930
2025-09-12 10:22:02,873 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_930
2025-09-12 10:22:02,873 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/1: 2023-11-01 (Parts to Labor Ratio)
2025-09-12 10:22:02,873 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_930
2025-09-12 10:22:04,306 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_930 - Parts to Labor Ratio
2025-09-12 10:22:06,343 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_930 legend is active
2025-09-12 10:22:06,343 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_930 is interactive after legend control...
2025-09-12 10:22:07,367 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_930
2025-09-12 10:22:16,906 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11-01
2025-09-12 10:22:16,906 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:22:16,906 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=drillDown
2025-09-12 10:22:16,906 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: CP Parts to Labor Ratio
2025-09-12 10:22:16,982 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: MPI Penetration Percentage (chart_1316) with 1 points
2025-09-12 10:22:17,722 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_1316
2025-09-12 10:22:43,248 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_1316
2025-09-12 10:22:43,249 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/1: 2023-11 (MPI Penetration Percentage)
2025-09-12 10:22:43,249 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1316
2025-09-12 10:22:44,669 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1316 - MPI Penetration Percentage
2025-09-12 10:22:46,700 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1316 legend is active
2025-09-12 10:22:46,700 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1316 is interactive after legend control...
2025-09-12 10:23:07,743 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1316
2025-09-12 10:23:27,317 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11
2025-09-12 10:23:27,317 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:23:27,317 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1316
2025-09-12 10:23:27,317 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: MPI Penetration Percentage
2025-09-12 10:23:27,401 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: Menu Penetration Percentage (chart_1317) with 1 points
2025-09-12 10:23:28,122 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_1317
2025-09-12 10:23:50,144 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_1317
2025-09-12 10:23:50,144 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/1: 2023-11 (Menu Penetration Percentage)
2025-09-12 10:23:50,145 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1317
2025-09-12 10:23:51,551 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1317 - Menu Penetration Percentage
2025-09-12 10:23:53,569 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1317 legend is active
2025-09-12 10:23:53,569 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1317 is interactive after legend control...
2025-09-12 10:23:54,598 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1317
2025-09-12 10:24:04,748 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2023-11
2025-09-12 10:24:04,748 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-12 10:24:04,748 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://ginnmotorcompany.fixedops.cc/AnalyzeData?chartId=1317
2025-09-12 10:24:04,748 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: Menu Penetration Percentage
2025-09-12 10:24:04,825 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Step 3: Saving results...
2025-09-12 10:24:04,829 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Complete chart processing workflow finished successfully
2025-09-12 10:24:04,829 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Final Summary:
2025-09-12 10:24:04,829 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) - Total combinations processed: 12
2025-09-12 10:24:04,829 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) - Total tasks completed: 35
2025-09-12 10:24:04,829 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) - Success rate: 74.3%
2025-09-12 10:24:42,660 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) HTML report generated: Individual_Reports-ginnmotorcompany/cp_overview_comparison.html
