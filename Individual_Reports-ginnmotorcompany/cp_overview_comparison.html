
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>UI vs DB Comparison Report</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body { padding: 20px; font-family: Arial, sans-serif; }
            .badge-pass { background-color: #28a745; color: white; }
            .badge-fail { background-color: #dc3545; color: white; }
            .card-header { cursor: pointer; background-color: #cfe2f3; }
            .comparison-section { display: flex; justify-content: space-between; margin-bottom: 15px; }
            .chart-info { background-color: #f8f9fa; padding: 15px; border-radius: 5px; flex: 1; margin-right: 10px; }
            .match-status { background-color: #e9ecef; padding: 15px; border-radius: 5px; flex: 0 0 200px; }
            .value-comparison { display: flex; justify-content: space-between; margin-top: 15px; }
            .ui-extracted { background-color: #f8f9fa; padding: 15px; border-radius: 5px; flex: 1; margin-right: 10px; }
            .db-calculated { background-color: #e9ecef; padding: 15px; border-radius: 5px; flex: 1; }
            .match-indicator { font-weight: bold; padding: 8px 15px; border-radius: 5px; display: inline-block; }
            .match-true { background-color: #d4edda; color: #155724; }
            .match-false { background-color: #f8d7da; color: #721c24; }
            .section-title { font-weight: bold; margin-bottom: 8px; color: #333; }
            .field-value { margin-bottom: 5px; }
            .badge-all-passed { background-color: #28a745; color: white; }
            .badge-has-failures { background-color: #dc3545; color: white; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="mb-4">UI vs DB Comparison Report</h1>
            <div class="mb-4">
                <strong>Tenant:</strong> ginnmotorcompany<br>
                <strong>Store:</strong> Ginn CJDR<br>
                <strong>Role:</strong> Admin<br>
                <strong>Generated At:</strong> 2025-09-12T10:24:42.659859<br>
                <strong>Report Timestamp:</strong> 20250912_102442<br>
            </div>

            <div class="d-flex gap-3 mb-4">
                <span class="badge bg-success">Passed: 0</span>
                <span class="badge bg-danger">Failed: 43</span>
                <span class="badge bg-secondary">Total: 43</span>
                <span class="badge bg-info">Match Rate: 0.0%</span>
            </div>

            <div class="accordion" id="reportAccordion">
    
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart0">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart0" aria-expanded="false" aria-controls="chart0">
                    Labor Sold Hours Percentage By Pay Type(chart_935) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(13 comparisons)</small>
                </button>
            </h2>
            <div id="chart0" class="accordion-collapse collapse" aria-labelledby="heading-chart0" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sale - Customer Pay</strong> (Customer Pay (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Customer Pay (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.57</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale - Customer Pay</div>
                            <div class="field-value"><strong>Value:</strong> 83724.5</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale - Customer Pay</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sold Hours - All Categories</strong> (Customer Pay (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Customer Pay (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.57</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours - All Categories</div>
                            <div class="field-value"><strong>Value:</strong> 1121.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours - All Categories</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sold Hours - Customer Pay</strong> (Customer Pay (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Customer Pay (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.57</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours - Customer Pay</div>
                            <div class="field-value"><strong>Value:</strong> 644.2</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours - Customer Pay</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sold Hours % - Customer Pay</strong> (Customer Pay (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Customer Pay (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.57</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours % - Customer Pay</div>
                            <div class="field-value"><strong>Value:</strong> 57.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours % - Customer Pay</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sold Hours - All Categories</strong> (Extended Service (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Extended Service (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.05</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours - All Categories</div>
                            <div class="field-value"><strong>Value:</strong> 1121.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours - All Categories</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sold Hours - Extended Service</strong> (Extended Service (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Extended Service (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.05</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours - Extended Service</div>
                            <div class="field-value"><strong>Value:</strong> 56.3</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours - Extended Service</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sold Hours % - Extended Service</strong> (Extended Service (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Extended Service (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.05</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours % - Extended Service</div>
                            <div class="field-value"><strong>Value:</strong> 5.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours % - Extended Service</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sold Hours - All Categories</strong> (Internal (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Internal (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.18</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours - All Categories</div>
                            <div class="field-value"><strong>Value:</strong> 1121.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours - All Categories</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sold Hours - Internal</strong> (Internal (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Internal (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.18</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours - Internal</div>
                            <div class="field-value"><strong>Value:</strong> 204.4</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours - Internal</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sold Hours % - Internal</strong> (Internal (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Internal (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.18</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours % - Internal</div>
                            <div class="field-value"><strong>Value:</strong> 18.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours % - Internal</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sold Hours - All Categories</strong> (Warranty (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Warranty (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.19</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours - All Categories</div>
                            <div class="field-value"><strong>Value:</strong> 1121.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours - All Categories</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sold Hours - Warranty</strong> (Warranty (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Warranty (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.19</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours - Warranty</div>
                            <div class="field-value"><strong>Value:</strong> 216.1</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours - Warranty</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sold Hours % - Warranty</strong> (Warranty (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Warranty (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.19</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours % - Warranty</div>
                            <div class="field-value"><strong>Value:</strong> 19.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours % - Warranty</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart1">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart1" aria-expanded="false" aria-controls="chart1">
                    CP 1-Line-RO Count(chart_948) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(6 comparisons)</small>
                </button>
            </h2>
            <div id="chart1" class="accordion-collapse collapse" aria-labelledby="heading-chart1" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Mileage Under 60K</strong> (Mileage Under 60k (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Mileage Under 60k (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 188.0</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Mileage Under 60K</div>
                            <div class="field-value"><strong>Value:</strong> 188.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Mileage Under 60K</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sale</strong> (Mileage Under 60k (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Mileage Under 60k (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 188.0</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale</div>
                            <div class="field-value"><strong>Value:</strong> 11625.95</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sold Hours</strong> (Mileage Under 60k (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Mileage Under 60k (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 188.0</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours</div>
                            <div class="field-value"><strong>Value:</strong> 99.7</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Mileage Over 60K</strong> (Mileage Over 60k (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Mileage Over 60k (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 215.0</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Mileage Over 60K</div>
                            <div class="field-value"><strong>Value:</strong> 215.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Mileage Over 60K</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sale</strong> (Mileage Over 60k (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Mileage Over 60k (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 215.0</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale</div>
                            <div class="field-value"><strong>Value:</strong> 18512.61</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sold Hours</strong> (Mileage Over 60k (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Mileage Over 60k (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 215.0</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours</div>
                            <div class="field-value"><strong>Value:</strong> 135.6</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart2">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart2" aria-expanded="false" aria-controls="chart2">
                    Multi-Line-RO Count Percentage(chart_1355) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(6 comparisons)</small>
                </button>
            </h2>
            <div id="chart2" class="accordion-collapse collapse" aria-labelledby="heading-chart2" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total RO Count</strong> (Total Shop (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Total Shop (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 31.35</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> 184.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total Shop</strong> (Total Shop (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Total Shop (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 31.35</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Shop</div>
                            <div class="field-value"><strong>Value:</strong> 18.15</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Shop</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total RO Count</strong> (Mileage Under 60K (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Mileage Under 60K (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 29.85</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> 80.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Mileage Under 60K</strong> (Mileage Under 60K (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Mileage Under 60K (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 29.85</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Mileage Under 60K</div>
                            <div class="field-value"><strong>Value:</strong> 30.42</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Mileage Under 60K</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total RO Count</strong> (Mileage Over 60K (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Mileage Over 60K (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 32.6</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> 104.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Mileage Over 60K</strong> (Mileage Over 60K (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Mileage Over 60K (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 32.6</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Mileage Over 60K</div>
                            <div class="field-value"><strong>Value:</strong> 33.55</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Mileage Over 60K</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart3">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart3" aria-expanded="false" aria-controls="chart3">
                    CP Parts to Labor Ratio By Category(chart_936) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(9 comparisons)</small>
                </button>
            </h2>
            <div id="chart3" class="accordion-collapse collapse" aria-labelledby="heading-chart3" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sale - Competitive</strong> (Competitive (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Competitive (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 3.37</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale - Competitive</div>
                            <div class="field-value"><strong>Value:</strong> 8392.91</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale - Competitive</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Parts Sale - Competitive</strong> (Competitive (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Competitive (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 3.37</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts Sale - Competitive</div>
                            <div class="field-value"><strong>Value:</strong> 28246.23</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts Sale - Competitive</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Parts To Labor Ratio - Competitive</strong> (Competitive (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Competitive (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 3.37</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts To Labor Ratio - Competitive</div>
                            <div class="field-value"><strong>Value:</strong> 3.37</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts To Labor Ratio - Competitive</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sale - Maintenance</strong> (Maintenance (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Maintenance (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 1.72</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale - Maintenance</div>
                            <div class="field-value"><strong>Value:</strong> 33313.1</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale - Maintenance</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Parts Sale - Maintenance</strong> (Maintenance (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Maintenance (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 1.72</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts Sale - Maintenance</div>
                            <div class="field-value"><strong>Value:</strong> 57448.85</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts Sale - Maintenance</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Parts To Labor Ratio - Maintenance</strong> (Maintenance (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Maintenance (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 1.72</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts To Labor Ratio - Maintenance</div>
                            <div class="field-value"><strong>Value:</strong> 1.72</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts To Labor Ratio - Maintenance</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sale - Repair</strong> (Repair (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Repair (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.95</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale - Repair</div>
                            <div class="field-value"><strong>Value:</strong> 42018.49</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale - Repair</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Parts Sale - Repair</strong> (Repair (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Repair (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.95</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts Sale - Repair</div>
                            <div class="field-value"><strong>Value:</strong> 39936.01</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts Sale - Repair</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Parts To Labor Ratio - Repair</strong> (Repair (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Repair (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.95</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts To Labor Ratio - Repair</div>
                            <div class="field-value"><strong>Value:</strong> 0.95</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts To Labor Ratio - Repair</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart4">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart4" aria-expanded="false" aria-controls="chart4">
                    CP Parts to Labor Ratio(chart_930) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(3 comparisons)</small>
                </button>
            </h2>
            <div id="chart4" class="accordion-collapse collapse" aria-labelledby="heading-chart4" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sale - Customer Pay</strong> (Parts to Labor Ratio (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Parts to Labor Ratio (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 1.5</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale - Customer Pay</div>
                            <div class="field-value"><strong>Value:</strong> 83724.5</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale - Customer Pay</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total Parts Sale</strong> (Parts to Labor Ratio (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Parts to Labor Ratio (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 1.5</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Parts Sale</div>
                            <div class="field-value"><strong>Value:</strong> 125631.09</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Parts Sale</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Parts To Labor Ratio</strong> (Parts to Labor Ratio (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Parts to Labor Ratio (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 1.5</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts To Labor Ratio</div>
                            <div class="field-value"><strong>Value:</strong> 1.5</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts To Labor Ratio</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart5">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart5" aria-expanded="false" aria-controls="chart5">
                    MPI Penetration Percentage(chart_1316) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(3 comparisons)</small>
                </button>
            </h2>
            <div id="chart5" class="accordion-collapse collapse" aria-labelledby="heading-chart5" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>MPI Count</strong> (MPI Penetration Percentage (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> MPI Penetration Percentage (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 1.25</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> MPI Count</div>
                            <div class="field-value"><strong>Value:</strong> 12.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> MPI Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total RO Count</strong> (MPI Penetration Percentage (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> MPI Penetration Percentage (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 1.25</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> 926.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>MPI Penetration %</strong> (MPI Penetration Percentage (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> MPI Penetration Percentage (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 1.25</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> MPI Penetration %</div>
                            <div class="field-value"><strong>Value:</strong> 1.3</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> MPI Penetration %</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart6">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart6" aria-expanded="false" aria-controls="chart6">
                    Menu Penetration Percentage(chart_1317) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(3 comparisons)</small>
                </button>
            </h2>
            <div id="chart6" class="accordion-collapse collapse" aria-labelledby="heading-chart6" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Menu Count</strong> (Menu Penetration Percentage (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Menu Penetration Percentage (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 1.53</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Menu Count</div>
                            <div class="field-value"><strong>Value:</strong> 9.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Menu Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total RO Count</strong> (Menu Penetration Percentage (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Menu Penetration Percentage (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 1.53</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> 587.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Menu Penetration %</strong> (Menu Penetration Percentage (2023-11-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Menu Penetration Percentage (2023-11-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 1.53</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Menu Penetration %</div>
                            <div class="field-value"><strong>Value:</strong> 1.53</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Menu Penetration %</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
            </div>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    