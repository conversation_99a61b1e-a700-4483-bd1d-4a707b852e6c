{"tenant": "ginnmotorcompany", "store": "Ginn CJDR", "role": "Admin", "generatedAt": "2025-09-12T10:24:42.659122", "results": [{"chart_name_with_id": "Labor Sold Hours Percentage By Pay Type(chart_935)", "line_name_legend": "Customer Pay (2023-11-01)", "drilldown_field": "Labor Sale - Customer Pay", "match": false, "ui": {"line_value": 0.57, "drilldown_value": 83724.5}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Labor Sold Hours Percentage By Pay Type(chart_935)", "line_name_legend": "Customer Pay (2023-11-01)", "drilldown_field": "Labor Sold Hours - All Categories", "match": false, "ui": {"line_value": 0.57, "drilldown_value": 1121.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Labor Sold Hours Percentage By Pay Type(chart_935)", "line_name_legend": "Customer Pay (2023-11-01)", "drilldown_field": "Labor Sold Hours - Customer Pay", "match": false, "ui": {"line_value": 0.57, "drilldown_value": 644.2}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Labor Sold Hours Percentage By Pay Type(chart_935)", "line_name_legend": "Customer Pay (2023-11-01)", "drilldown_field": "Labor Sold Hours % - Customer Pay", "match": false, "ui": {"line_value": 0.57, "drilldown_value": 57.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Labor Sold Hours Percentage By Pay Type(chart_935)", "line_name_legend": "Extended Service (2023-11-01)", "drilldown_field": "Labor Sold Hours - All Categories", "match": false, "ui": {"line_value": 0.05, "drilldown_value": 1121.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Labor Sold Hours Percentage By Pay Type(chart_935)", "line_name_legend": "Extended Service (2023-11-01)", "drilldown_field": "Labor Sold Hours - Extended Service", "match": false, "ui": {"line_value": 0.05, "drilldown_value": 56.3}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Labor Sold Hours Percentage By Pay Type(chart_935)", "line_name_legend": "Extended Service (2023-11-01)", "drilldown_field": "Labor Sold Hours % - Extended Service", "match": false, "ui": {"line_value": 0.05, "drilldown_value": 5.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Labor Sold Hours Percentage By Pay Type(chart_935)", "line_name_legend": "Internal (2023-11-01)", "drilldown_field": "Labor Sold Hours - All Categories", "match": false, "ui": {"line_value": 0.18, "drilldown_value": 1121.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Labor Sold Hours Percentage By Pay Type(chart_935)", "line_name_legend": "Internal (2023-11-01)", "drilldown_field": "Labor Sold Hours - Internal", "match": false, "ui": {"line_value": 0.18, "drilldown_value": 204.4}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Labor Sold Hours Percentage By Pay Type(chart_935)", "line_name_legend": "Internal (2023-11-01)", "drilldown_field": "Labor Sold Hours % - Internal", "match": false, "ui": {"line_value": 0.18, "drilldown_value": 18.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Labor Sold Hours Percentage By Pay Type(chart_935)", "line_name_legend": "Warranty (2023-11-01)", "drilldown_field": "Labor Sold Hours - All Categories", "match": false, "ui": {"line_value": 0.19, "drilldown_value": 1121.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Labor Sold Hours Percentage By Pay Type(chart_935)", "line_name_legend": "Warranty (2023-11-01)", "drilldown_field": "Labor Sold Hours - Warranty", "match": false, "ui": {"line_value": 0.19, "drilldown_value": 216.1}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Labor Sold Hours Percentage By Pay Type(chart_935)", "line_name_legend": "Warranty (2023-11-01)", "drilldown_field": "Labor Sold Hours % - Warranty", "match": false, "ui": {"line_value": 0.19, "drilldown_value": 19.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count(chart_948)", "line_name_legend": "Mileage Under 60k (2023-11-01)", "drilldown_field": "Mileage Under 60K", "match": false, "ui": {"line_value": 188.0, "drilldown_value": 188.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count(chart_948)", "line_name_legend": "Mileage Under 60k (2023-11-01)", "drilldown_field": "Labor Sale", "match": false, "ui": {"line_value": 188.0, "drilldown_value": 11625.95}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count(chart_948)", "line_name_legend": "Mileage Under 60k (2023-11-01)", "drilldown_field": "Labor Sold Hours", "match": false, "ui": {"line_value": 188.0, "drilldown_value": 99.7}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count(chart_948)", "line_name_legend": "Mileage Over 60k (2023-11-01)", "drilldown_field": "Mileage Over 60K", "match": false, "ui": {"line_value": 215.0, "drilldown_value": 215.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count(chart_948)", "line_name_legend": "Mileage Over 60k (2023-11-01)", "drilldown_field": "Labor Sale", "match": false, "ui": {"line_value": 215.0, "drilldown_value": 18512.61}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count(chart_948)", "line_name_legend": "Mileage Over 60k (2023-11-01)", "drilldown_field": "Labor Sold Hours", "match": false, "ui": {"line_value": 215.0, "drilldown_value": 135.6}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Multi-Line-RO Count Percentage(chart_1355)", "line_name_legend": "Total Shop (2023-11-01)", "drilldown_field": "Total RO Count", "match": false, "ui": {"line_value": 31.35, "drilldown_value": 184.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Multi-Line-RO Count Percentage(chart_1355)", "line_name_legend": "Total Shop (2023-11-01)", "drilldown_field": "Total Shop", "match": false, "ui": {"line_value": 31.35, "drilldown_value": 18.15}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Multi-Line-RO Count Percentage(chart_1355)", "line_name_legend": "Mileage Under 60K (2023-11-01)", "drilldown_field": "Total RO Count", "match": false, "ui": {"line_value": 29.85, "drilldown_value": 80.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Multi-Line-RO Count Percentage(chart_1355)", "line_name_legend": "Mileage Under 60K (2023-11-01)", "drilldown_field": "Mileage Under 60K", "match": false, "ui": {"line_value": 29.85, "drilldown_value": 30.42}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Multi-Line-RO Count Percentage(chart_1355)", "line_name_legend": "Mileage Over 60K (2023-11-01)", "drilldown_field": "Total RO Count", "match": false, "ui": {"line_value": 32.6, "drilldown_value": 104.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Multi-Line-RO Count Percentage(chart_1355)", "line_name_legend": "Mileage Over 60K (2023-11-01)", "drilldown_field": "Mileage Over 60K", "match": false, "ui": {"line_value": 32.6, "drilldown_value": 33.55}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio By Category(chart_936)", "line_name_legend": "Competitive (2023-11-01)", "drilldown_field": "Labor Sale - Competitive", "match": false, "ui": {"line_value": 3.37, "drilldown_value": 8392.91}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio By Category(chart_936)", "line_name_legend": "Competitive (2023-11-01)", "drilldown_field": "Parts Sale - Competitive", "match": false, "ui": {"line_value": 3.37, "drilldown_value": 28246.23}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio By Category(chart_936)", "line_name_legend": "Competitive (2023-11-01)", "drilldown_field": "Parts To Labor Ratio - Competitive", "match": false, "ui": {"line_value": 3.37, "drilldown_value": 3.37}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio By Category(chart_936)", "line_name_legend": "Maintenance (2023-11-01)", "drilldown_field": "Labor Sale - Maintenance", "match": false, "ui": {"line_value": 1.72, "drilldown_value": 33313.1}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio By Category(chart_936)", "line_name_legend": "Maintenance (2023-11-01)", "drilldown_field": "Parts Sale - Maintenance", "match": false, "ui": {"line_value": 1.72, "drilldown_value": 57448.85}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio By Category(chart_936)", "line_name_legend": "Maintenance (2023-11-01)", "drilldown_field": "Parts To Labor Ratio - Maintenance", "match": false, "ui": {"line_value": 1.72, "drilldown_value": 1.72}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio By Category(chart_936)", "line_name_legend": "Repair (2023-11-01)", "drilldown_field": "Labor Sale - Repair", "match": false, "ui": {"line_value": 0.95, "drilldown_value": 42018.49}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio By Category(chart_936)", "line_name_legend": "Repair (2023-11-01)", "drilldown_field": "Parts Sale - Repair", "match": false, "ui": {"line_value": 0.95, "drilldown_value": 39936.01}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio By Category(chart_936)", "line_name_legend": "Repair (2023-11-01)", "drilldown_field": "Parts To Labor Ratio - Repair", "match": false, "ui": {"line_value": 0.95, "drilldown_value": 0.95}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio(chart_930)", "line_name_legend": "Parts to Labor Ratio (2023-11-01)", "drilldown_field": "Labor Sale - Customer Pay", "match": false, "ui": {"line_value": 1.5, "drilldown_value": 83724.5}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio(chart_930)", "line_name_legend": "Parts to Labor Ratio (2023-11-01)", "drilldown_field": "Total Parts Sale", "match": false, "ui": {"line_value": 1.5, "drilldown_value": 125631.09}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio(chart_930)", "line_name_legend": "Parts to Labor Ratio (2023-11-01)", "drilldown_field": "Parts To Labor Ratio", "match": false, "ui": {"line_value": 1.5, "drilldown_value": 1.5}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "MPI Penetration Percentage(chart_1316)", "line_name_legend": "MPI Penetration Percentage (2023-11-01)", "drilldown_field": "MPI Count", "match": false, "ui": {"line_value": 1.25, "drilldown_value": 12.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "MPI Penetration Percentage(chart_1316)", "line_name_legend": "MPI Penetration Percentage (2023-11-01)", "drilldown_field": "Total RO Count", "match": false, "ui": {"line_value": 1.25, "drilldown_value": 926.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "MPI Penetration Percentage(chart_1316)", "line_name_legend": "MPI Penetration Percentage (2023-11-01)", "drilldown_field": "MPI Penetration %", "match": false, "ui": {"line_value": 1.25, "drilldown_value": 1.3}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Menu Penetration Percentage(chart_1317)", "line_name_legend": "Menu Penetration Percentage (2023-11-01)", "drilldown_field": "<PERSON><PERSON>", "match": false, "ui": {"line_value": 1.53, "drilldown_value": 9.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Menu Penetration Percentage(chart_1317)", "line_name_legend": "Menu Penetration Percentage (2023-11-01)", "drilldown_field": "Total RO Count", "match": false, "ui": {"line_value": 1.53, "drilldown_value": 587.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Menu Penetration Percentage(chart_1317)", "line_name_legend": "Menu Penetration Percentage (2023-11-01)", "drilldown_field": "Menu Penetration %", "match": false, "ui": {"line_value": 1.53, "drilldown_value": 1.53}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}]}